package main

import (
	"fmt"
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/routers"
	"next-meeting-backend/internal/pkg/config"
	"next-meeting-backend/internal/pkg/zaplog"

	"github.com/gin-gonic/gin"
)

func init() {

	if config.App.RunMode == "prod" {
		gin.SetMode(gin.ReleaseMode)
		l := zaplog.InitProdEncoderConfig(config.App.ZapConfig)
		defer zaplog.RevokeZap(l)
	} else {
		gin.SetMode(gin.DebugMode)
		l := zaplog.InitDevEncoderConfig()
		defer zaplog.RevokeZap(l)
	}

	//初始化数据库
	models.BuildPostgresEngine(config.App.DbConfig.Psql)

	//初始化Casbin
	models.InitCasbin()
}

func main() {
	router := routers.SetupRouter()
	runPort := config.App.Port
	if runPort == 0 {
		runPort = 3001
	}
	err := router.Run(fmt.Sprintf(":%d", runPort))
	if err != nil {
		return
	}
}
