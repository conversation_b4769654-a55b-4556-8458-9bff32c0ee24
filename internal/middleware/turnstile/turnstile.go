package turnstile

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"
	"github.com/spf13/viper"
)

type TurnstileConfig struct {
	SecretKey string
}

type TurnstileResponse struct {
	Success     bool     `json:"success"`
	ErrorCodes  []string `json:"error-codes"`
	ChallengeTS string   `json:"challenge_ts"`
	Hostname    string   `json:"hostname"`
}

func Turnstile() gin.HandlerFunc {
	secretKey := viper.GetString("turnstile.secret")
	return func(c *gin.Context) {
		// 从表单或 JSON 中获取 token
		token := c.PostForm("cf-turnstile-response")
		if token == "" {
			// 尝试从 JSON 请求体中获取
			var jsonBody struct {
				Token string `json:"cf-turnstile-response"`
			}
			if err := c.ShouldBindBodyWithJSON(&jsonBody); err == nil {
				token = jsonBody.Token
			}
		}

		if token == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error": "Turnstile token is required",
			})
			return
		}

		// 验证 token
		valid, err := verifyTurnstileToken(token, secretKey)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to verify Turnstile token",
			})
			return
		}

		if !valid {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error": "Invalid Turnstile token",
			})
			return
		}

		// 验证通过，继续处理
		c.Next()
	}
}
func verifyTurnstileToken(token, secretKey string) (bool, error) {
	postBody := map[string]string{
		"secret":   secretKey,
		"response": token,
	}
	jsonBody, _ := json.Marshal(postBody)

	resp, err := http.Post(
		"https://challenges.cloudflare.com/turnstile/v0/siteverify",
		"application/json",
		bytes.NewBuffer(jsonBody),
	)
	if err != nil {
		if errors.Is(err, io.EOF) {
			return true, nil
		}
		return false, fmt.Errorf("failed to verify token: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, fmt.Errorf("failed to read response: %v", err)
	}

	var result TurnstileResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return false, fmt.Errorf("failed to parse response: %v", err)
	}

	return result.Success, nil
}
