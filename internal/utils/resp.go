package utils

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"next-meeting-backend/internal/pkg/consts"
	"strings"
)

type responseStruct struct {
	StatusCode int         `json:"code"`
	Message    string      `json:"msg"`
	Data       interface{} `json:"data"`
}

func ginResponse(c *gin.Context, httpCode int, errCode int, message string, data interface{}) {
	c.JSON(httpCode, responseStruct{
		StatusCode: errCode,
		Message:    message,
		Data:       data,
	})
}

func RespondWithError(c *gin.Context, errCode int, msg ...string) {
	var httpCode int
	var message string

	switch errCode {
	case consts.InvalidParams:
		httpCode = http.StatusBadRequest
	case consts.ErrorAuthCheckTokenFail,
		consts.ErrorPasswordIncorrect,
		consts.ErrorAuthCheckTokenTimeout,
		consts.ErrorInvalidCredentials:
		httpCode = http.StatusUnauthorized
	case consts.ErrorAuthCheckPermissionDenied:
		httpCode = http.StatusForbidden
	case consts.ErrorUserNotFound,
		consts.NotFound:
		httpCode = http.StatusNotFound
	default:
		httpCode = http.StatusInternalServerError
		break
	}

	if msg != nil {
		message = strings.Join(msg, "")
	} else {
		message = consts.GetMsg(errCode)
	}

	ginResponse(c, httpCode, errCode, message, nil)
}

func RespondWithSuccess(c *gin.Context, data interface{}, msg ...string) {
	var message string
	if msg != nil {
		message = msg[0]
	}
	ginResponse(c, http.StatusOK, consts.Success, message, data)
}
