package service

import (
	"context"
	"errors"
	"mime/multipart"
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/models_field"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/pkg/s3"

	"go.uber.org/zap"
)

type AbstractService struct {
	models.Abstracts
	models.Themes
}

func (s *AbstractService) SubmitAbstract(file multipart.File, fileHeader *multipart.FileHeader) error {
	um := models.UserModel{}
	usm := models.Users{Id: s.UserId}
	model := models.AbstractModel{Users: usm}
	_, err := um.GetUser(&usm)
	if err != nil {
		zap.S().Error(err.Error())
		return err
	}
	//if !usm.Paid {
	//	return errors.New("user has not paid")
	//}

	ctx := context.Background()
	filename, err := s3.ProcessAndUploadReceipt(ctx, file, fileHeader, s.UserId, consts.RemoteAbstractPath)
	if err != nil {
		zap.S().Error(err.Error())
		return err
	}

	s.Filename = fileHeader.Filename
	s.FilePath = filename

	// 检查用户是否已经提交过摘要
	res, err := model.GetAbstract("user_id = ?", s.UserId)
	if err != nil {
		zap.S().Error(err.Error())
		return err
	}
	if res != nil {
		s.Abstracts.Id = res.Abstracts.Id
		_, err = model.UpdateAbstract(&s.Abstracts, "all")
		if err != nil {
			zap.S().Error(err.Error())
			return err
		}
	} else {
		_, err = model.AddAbstract(&s.Abstracts)
		if err != nil {
			zap.S().Error(err.Error())
			return err
		}
	}

	return nil
}

func (s *AbstractService) UpdateAbstract(file multipart.File, fileHeader *multipart.FileHeader) error {
	am := models.AbstractModel{}
	// 1. 检查摘要是否存在
	abstract, err := am.GetAbstract("a.id = ?", s.Abstracts.Id)
	if err != nil {
		zap.S().Error(err.Error())
		return err
	}
	if abstract == nil {
		return errors.New("abstract not found")
	}

	var cols []string
	cols = append(cols,

		models_field.AbstractTitle,
		models_field.AbstractTheme,
		models_field.AbstractPublicationStatus,
		models_field.AbstractOralPresentation,
		models_field.AbstractPosterPresentation,
	)

	if file != nil {
		ctx := context.Background()
		filename, err := s3.ProcessAndUploadReceipt(ctx, file, fileHeader, s.UserId, consts.RemoteAbstractPath)
		if err != nil {
			zap.S().Error(err.Error())
			return err
		}
		s.FilePath = filename
		s.Filename = fileHeader.Filename
		cols = append(cols, models_field.AbstractFilename)
		cols = append(cols, models_field.AbstractFilePath)
	}

	// 3. 更新摘要
	_, err = am.UpdateAbstract(&s.Abstracts, cols...)
	if err != nil {
		return err
	}
	return nil
}

func (s *AbstractService) GetAbstract() (*models.AbstractInfo, error) {
	am := models.AbstractModel{}
	abstract, err := am.GetAbstract("user_id = ?", s.Abstracts.UserId)
	if err != nil {
		zap.S().Error(err.Error())
		return nil, err
	}
	if abstract == nil {
		return nil, nil
	}
	ctx := context.Background()
	url, err := s3.GetPresignedURL(ctx, abstract.FilePath, abstract.Abstracts.Id)
	if err != nil {
		zap.S().Error(err.Error())
		return nil, err
	}
	abstract.FilePath = url

	return abstract, nil
}
