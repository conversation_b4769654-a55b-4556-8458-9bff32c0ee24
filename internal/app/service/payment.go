package service

import (
	"context"
	"errors"
	"fmt"
	"mime/multipart"
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/models_field"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/pkg/email"
	"next-meeting-backend/internal/pkg/s3"
	"strings"
	"time"

	"go.uber.org/zap"
)

type PaymentService struct {
	models.Users
	models.Transactions
	models.Reviews
	models.Invoices
}

// AddPayment 创建支付记录
// approved为false时才允许添加，
// 没有被review并且没有payment信息时允许添加
func (s *PaymentService) AddPayment(file multipart.File, fileHeader *multipart.FileHeader) error {
	userId := s.Users.Id
	if userId == 0 {
		zap.S().Error("UserId is zero")
		return errors.New("user id is nil")
	}
	m := models.PaymentModel{}
	um := models.UserModel{}
	data, err := m.CheckPaymentStatusByUserId(s.Users.Id)
	if err != nil {
		return err
	}
	// 首先需要知道data是不是空的，如果不是空的则继续判断，是空的则直接添加
	if len(data) > 0 {
		ap := data[0]["approved"].(bool)
		// 已经提交并且approve后不允许修改
		if ap == true {
			return errors.New("payment already approved")
		}
	}

	ctx := context.Background()
	filename, err := s3.ProcessAndUploadReceipt(ctx, file, fileHeader, userId, consts.RemoteReceiptPath)
	if err != nil {
		return err
	}

	s.Evidence = filename
	if _, err = m.AddTransaction(s.Transactions,
		models_field.TransactionRequestUser,
		models_field.TransactionTeamUsers,
		models_field.TransactionMethod,
		models_field.TransactionRegion,
		models_field.TransactionTransactionId,
		models_field.TransactionAmount,
		models_field.TransactionNotes,
		models_field.TransactionPaymentTime,
		models_field.TransactionEvidence,
		models_field.TransactionMethodId,
	); err != nil {
		return err
	}
	err = um.UpdateUserPaidStatusById(userId, true)
	return nil
}

// UpdatePayment 修改支付记录
// approved状态为true时不允许添加和修改，
// 没有被review并且有payment记录后不允许添加，允许修改
func (s *PaymentService) UpdatePayment(file multipart.File, fileHeader *multipart.FileHeader) error {
	id := s.Transactions.Id
	if id == 0 {
		zap.S().Error("UserId is zero")
		return errors.New("user id is nil")
	}
	m := models.PaymentModel{}
	data, err := m.CheckPaymentStatusById(id)
	if err != nil {
		return err
	}
	// 首先需要知道data是不是空的，如果不是空的则继续判断，是空的则直接添加
	if len(data) > 0 {
		//先看有没有tid, 如果没有tid则不允许修改
		if data[0]["tid"] == nil {
			return errors.New("can't find transaction")
		}

		// 上面一步已经知道已经有了支付信息，接下来看审核状态
		// 如果被审核了则不允许修改
		if data[0]["approved"] != nil {
			return errors.New("payment already reviewed")
		}
	}
	userId := data[0]["request_user"].(int32)
	ctx := context.Background()

	payment := models.Transactions{
		Id:              s.Transactions.Id,
		TeamUsers:       s.Transactions.TeamUsers,
		MethodId:        s.MethodId,
		Region:          s.Transactions.Region,
		TransactionId:   s.Transactions.TransactionId,
		Amount:          s.Transactions.Amount,
		Notes:           s.Transactions.Notes,
		TransactionTime: s.Transactions.TransactionTime,
	}

	var f []string
	f = append(f, models_field.TransactionTeamUsers,
		models_field.TransactionMethod,
		models_field.TransactionRegion,
		models_field.TransactionTransactionId,
		models_field.TransactionAmount,
		models_field.TransactionNotes,
		models_field.TransactionPaymentTime,
		models_field.TransactionMethodId,
	)

	if file != nil {
		filename, err := s3.ProcessAndUploadReceipt(ctx, file, fileHeader, int(userId), consts.RemoteReceiptPath)
		if err != nil {
			return err
		}
		payment.Evidence = filename
		f = append(f, models_field.TransactionEvidence)
	}

	if _, err := m.UpdateTransaction(payment,
		f...,
	); err != nil {
		return err
	}
	return nil
}

func (s *PaymentService) GetEvidence(id int) (string, error) {
	pm := models.PaymentModel{}
	transaction, err := pm.GetTransactionById(id)
	if err != nil {
		return "", err
	}

	evidence := transaction.Evidence
	ctx := context.Background()
	// 如果没有审核过，那么将图片加载出来让管理员审核
	url, err := s3.GetPresignedURL(ctx, evidence, id)
	if err != nil {
		return "", err
	}
	return url, nil
}

func (s *PaymentService) DeletePayment() error {
	return nil
}

func (s *PaymentService) GetPayment() (*models.TransactionInfo, error) {
	pm := models.PaymentModel{}
	userId := s.Users.Id
	if userId == 0 {
		zap.S().Error("UserId is zero")
		return nil, errors.New("user id is nil")
	}

	payment, err := pm.GetTransactionByUserId(userId)
	if err != nil {
		return nil, err
	}

	// payment有可能是空的
	if payment == nil {
		return nil, nil
	}

	ctx := context.Background()
	url, err := s3.GetPresignedURL(ctx, payment.Evidence, payment.Tid)
	if err != nil {
		return nil, err
	}
	payment.Evidence = url

	return payment, err
}

// ListPayments 这是admin管理页的一项功能，用于查询出符合条件的缴费信息用于审核
// 需要的条件为：name或email或transaction id，review status
// 第一项条件在同一个搜索框内，所以name = f or email = f or transaction id = f
// 第二个条件只需要判断review_id是否为null即可

func (s *PaymentService) ListPayments(limit, offset int, filter, status string) ([]*models.TransactionInfo, int64, error) {
	m := models.PaymentModel{}
	var filterMap map[string]interface{}

	if filter != "" {
		filterMap = make(map[string]interface{})
		filterMap["transaction_id LIKE ?"] = "%" + filter + "%"
		filterMap["email LIKE ?"] = "%" + filter + "%"
		filterMap["username LIKE ?"] = "%" + filter + "%"
	}

	statusMap := make(map[string]interface{})
	switch status {
	case "pending":
		statusMap["approved IS NULL"] = nil
	case "approved":
		statusMap["approved = ?"] = true
	case "rejected":
		statusMap["approved = ?"] = false
	}

	var transactionsInfo []*models.TransactionInfo
	err := m.ListTransactions(&transactionsInfo, filterMap, statusMap, limit, offset)
	if err != nil {
		return nil, 0, err
	}
	count, err := m.CountTransactions(filterMap, statusMap)

	for _, v := range transactionsInfo {
		v.Amount = fmt.Sprintf("%v", v.Amount)
		v.Region = fmt.Sprintf("%v", v.Region)
	}

	return transactionsInfo, count, err
}

// AddInvoiceInfo 添加发票信息
func (s *PaymentService) AddInvoiceInfo() error {
	if &s.Invoices == nil {
		zap.S().Error("invoice info is nil")
		return errors.New("invoice info is nil")
	}
	m := models.PaymentModel{}
	_, err := m.AddInvoice(&s.Invoices)
	if err != nil {
		return err
	}
	return nil
}

func (s *PaymentService) UpdateInvoiceInfo() error {
	if &s.Invoices == nil {
		zap.S().Error("invoice info is nil")
		return errors.New("invoice info is nil")
	}
	m := models.PaymentModel{}
	_, err := m.UpdateInvoice(&s.Invoices)
	if err != nil {
		return err
	}
	return nil
}

func (s *PaymentService) GetInvoiceInfo() (*models.Invoices, error) {
	userId := s.Users.Id
	if userId == 0 {
		zap.S().Error("user id is nil")
		return nil, errors.New("user id is nil")
	}
	m := models.PaymentModel{}
	invoice, err := m.GetInvoiceByUserId(userId)
	if err != nil {
		return nil, err
	}
	return invoice, err
}

func (s *PaymentService) AddReview() error {
	if &s.Reviews == nil {
		zap.S().Error("review info is nil")
		return errors.New("review info is nil")
	}
	sess := models.NewTransaction()
	um := models.UserModel{}
	m := models.PaymentModel{}
	// 审核通过

	sess.BeginTransaction()
	err := m.AddReview(&s.Reviews)
	if err != nil {
		return err
	}

	// 通过后需要将用户缴费标识改正
	// 如果被拒绝那么直接退出
	if !s.Reviews.Approved {
		return nil
	}
	// 下面获取tid, 根据tid内的team_users获取同组缴费的人员
	tid := s.Reviews.Tid
	transaction, err := m.GetTransactionById(tid)
	if err != nil {
		return err
	}
	if transaction == nil {
		return errors.New("transaction not found")
	}

	teamUsers := strings.Split(transaction.TeamUsers, ",")

	for _, username := range teamUsers {
		username = strings.TrimSpace(username)
		uid := um.GetUserIdByUsername(username)
		if uid == 0 {
			sess.RollbackTransaction()
			return errors.New("user not found")
		}
		// 通过后需要将用户缴费标识改正
		err = um.UpdateUserPaidStatusById(uid, true)
		if err != nil {
			sess.RollbackTransaction()
			return err
		}
	}
	sess.CommitTransaction()
	t, err := m.GetTransactionByUserId(transaction.RequestUser)
	if err != nil || t == nil {
		return err
	}
	em := email.GetEmailService()
	go func() {
		err = em.SendPaymentSuccessEmail(t.Email,
			t.RequestUsername,
			t.TransactionId,
			t.Amount,
			t.MethodName,
			t.TransactionTime.Format(time.DateTime),
			t.CreateTime.Format(time.DateTime),
			t.Region)
		if err != nil {
			zap.S().Errorf("send email error: %s", err.Error())
		}
	}()

	return nil
}
