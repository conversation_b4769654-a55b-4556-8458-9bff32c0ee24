package service

import (
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/models_field"
	"strconv"

	"go.uber.org/zap"
)

// PolicyService handles operations related to Casbin policies
type PolicyService struct {
}

// InitializeBasicPolicies sets up the basic policies for the application
// This should be called during application setup or by an admin
func InitializeBasicPolicies() {
	// Define basic roles
	// Admin role has access to everything
	models.AddPolicy("admin", "*", "*")

	// User role has limited access
	models.AddPolicy("user", "user", "read")
	models.AddPolicy("user", "user", "update")

	zap.S().Info("Basic policies initialized")
}

// AssignRoleToUser assigns a role to a user
func (s *PolicyService) AssignRoleToUser(userId int, role string) (bool, error) {
	userStr := strconv.Itoa(userId)
	if ok, err := models.AddRoleForUser(userStr, role); err != nil {
		zap.S().Errorf("Failed to assign role %s to user %d: %v", role, userId, err)
		return ok, err
	} else if !ok {
		zap.S().Warnf("Failed to assign role %s to user %d. Role may already exist for the user.", role, userId)
		return false, nil
	}
	return true, nil
}

// RemoveRoleFromUser removes a role from a user
func (s *PolicyService) RemoveRoleFromUser(userId int, role string) bool {
	userStr := strconv.Itoa(userId)
	return models.RemoveRoleForUser(userStr, role)
}

// GetUserRoles gets all roles for a user
func (s *PolicyService) GetUserRoles(userId int) []string {
	userStr := strconv.Itoa(userId)
	return models.GetRolesForUser(userStr)
}

// AddPermissionForRole adds a permission for a role
func AddPermissionForRole(role, obj, act string) bool {
	return models.AddPolicy(role, obj, act)
}

// RemovePermissionForRole removes a permission from a role
func RemovePermissionForRole(role, obj, act string) bool {
	return models.RemovePolicy(role, obj, act)
}

// CheckUserPermission checks if a user has permission to access a resource
func (s *PolicyService) CheckUserPermission(userId int, obj, act string) bool {
	userStr := strconv.Itoa(userId)
	return models.CheckPermission(userStr, obj, act)
}

// ReloadPolicies manually reloads all policies from database
func ReloadPolicies() error {
	return models.ReloadPolicy()
}

// SaveAndReloadPolicies saves and reloads all policies
func SaveAndReloadPolicies() error {
	return models.SaveAndReloadPolicy()
}

// GetAllPolicies returns all policy rules
func GetAllPolicies() [][]string {
	return models.GetAllPolicies()
}

// GetAllRoles returns all role assignments
func GetAllRoles() ([][]string, error) {
	return models.GetAllRoles()
}

// ClearAllPolicies clears all policies and roles (admin only)
func ClearAllPolicies() error {
	return models.ClearAllPolicies()
}

func GetDefaultRole() (int, string) {
	return models_field.RoleNormalUserId, models_field.RoleNormalUser
}

func GetRoleNameById(id int) string {
	switch id {
	case models_field.RoleAdminId:
		return models_field.RoleAdmin
	case models_field.RoleNormalUserId:
		return models_field.RoleNormalUser
	default:
		return models_field.RoleNormalUser
	}
}

func GetRoleIdByName(name string) int {
	switch name {
	case models_field.RoleAdmin:
		return models_field.RoleAdminId
	case models_field.RoleNormalUser:
		return models_field.RoleNormalUserId
	default:
		return models_field.RoleNormalUserId
	}
}
