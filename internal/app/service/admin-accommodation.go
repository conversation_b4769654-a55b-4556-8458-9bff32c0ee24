package service

import "next-meeting-backend/internal/app/models"

type AdminAccommodationService struct {
}

func (receiver *AdminAccommodationService) ListHotels() ([]models.Hotels, error) {
	am := models.AccommodationModel{}
	return am.GetHotels()
}

// AddHotels TODO: 以后再加
func (receiver *AdminAccommodationService) AddHotels() {

}

func (receiver *AdminAccommodationService) ListRooms() ([]models.RoomInfo, error) {
	am := models.AccommodationModel{}
	var rooms []models.RoomInfo
	err := am.GetRoomsInfo(&rooms)
	if err != nil {
		return nil, err
	}
	return rooms, err
}

// ListReservations 列出所有的预定信息
func (receiver *AdminAccommodationService) ListReservations() (int64, []models.RoomReservationInfo, error) {
	am := models.AccommodationModel{}
	var reservations []models.RoomReservationInfo
	count, err := am.GetRoomReservations(&reservations)
	if err != nil {
		return 0, nil, err
	}

	return count, reservations, nil
}

func (receiver *AdminAccommodationService) Statistics() (int64, int64, int64, int64, error) {
	return 0, 0, 0, 0, nil
}
