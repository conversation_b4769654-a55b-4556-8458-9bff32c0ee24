package service

import (
	"errors"
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/pkg/email"

	"go.uber.org/zap"
)

type EmailService struct {
	models.Tokens
	models.Emails
	models.Users
}

func (s *EmailService) UpdateUserEmail(userId int, newEmail string) error {
	ts := tokenService{}
	um := models.UserModel{}
	em := models.EmailModel{}
	// 先找出用户，由用户找出email信息
	user, err := um.GetUserById(userId)
	if err != nil {
		zap.S().Errorf("Failed to get user: %v", err)
		return err
	}

	if user.Users.Id == 0 {
		return errors.New("user not found")
	}

	// Check if the new email is already in use
	existingEmail, err := em.GetEmailByAddress(newEmail)
	if err != nil {
		zap.S().Errorf("Failed to check email existence: %v", err)
		return err
	}

	// email不能有其他人正在使用
	if existingEmail != nil {
		return errors.New("email already in use")
	}

	// email必须未通过验证，如果验证过的email想要修改，需要通过管理员进行修改
	if user.Emails.EmailVerified {
		return errors.New("can't change already verified email")
	}

	user.Emails.Email = newEmail
	// Update the user's email
	emailId, err := em.UpdateEmail(&user.Emails, "email")
	if err != nil || emailId == 0 {
		return errors.New("failed to update email")
	}
	token, link := email.BuildVerifyEmailLink()
	err = ts.AddTokenToDb(user.Users.Id, token)
	if err != nil {
		return err
	}

	esc := email.GetEmailService()
	err = esc.SendVerificationEmail(newEmail, user.Users.Username, link)
	if err != nil {
		zap.S().Errorf("Failed to send verification email to %s: %v", newEmail, err)
		return errors.New("failed to send verification email")
	}

	return nil
}

// VerifyEmail verifies a user's email using the verification token
func (s *EmailService) VerifyEmail(token string) error {
	tm := models.TokensModel{}
	um := models.UserModel{}
	em := models.EmailModel{}
	// 先找出token对应的user id
	s.Token = token
	tokensRecord, err := tm.GetToken(&s.Tokens)
	if err != nil {
		return errors.New("invalid verification token")
	}

	// 通过user id找到email id
	u := models.Users{Id: tokensRecord.UserId}
	_, err = um.GetUser(&u)
	if err != nil {
		return errors.New("user not found")
	}

	// 通过email id直接完成验证
	success := em.VerifyEmail(u.EmailId)
	if !success {
		return errors.New("failed to verify email")
	}
	err = tm.RemoveToken(tokensRecord.Token)
	if err != nil {
		return errors.New("failed to remove token")
	}

	return nil
}

// ResendVerificationEmail resends the verification email to the user
func (s *EmailService) ResendVerificationEmail(emailAddress string) error {
	um := models.UserModel{}
	em := models.EmailModel{}
	ts := tokenService{}
	esc := email.GetEmailService()
	// Get email record by email address
	emailRecord, err := em.GetEmailByAddress(emailAddress)
	if err != nil || emailRecord == nil {
		return errors.New("email not found")
	}

	// Get user by email ID
	user, err := um.GetUserByEmailId(emailRecord.Id)
	if err != nil || user == nil {
		return errors.New("user not found for this email")
	}

	// Check if email is already verified
	if emailRecord.EmailVerified {
		return errors.New("email is already verified")
	}

	// Generate a new verification token
	token, link := email.BuildVerifyEmailLink()

	err = ts.AddTokenToDb(user.Id, token)
	if err != nil {
		return errors.New("failed to update verification token")
	}

	// Send verification email
	err = esc.SendVerificationEmail(emailAddress, user.Username, link)
	if err != nil {
		zap.S().Errorf("Failed to send verification email to %s: %v", emailAddress, err)
		return errors.New("failed to send verification email")
	}
	zap.S().Infof("Verification email sent to %s", emailAddress)

	return nil
}
