package service

import (
	"context"
	"errors"
	"mime/multipart"
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/models_field"
	"next-meeting-backend/internal/pkg/bcrypt"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/pkg/email"
	"next-meeting-backend/internal/pkg/jwt"
	"next-meeting-backend/internal/pkg/s3"
	"next-meeting-backend/internal/utils"
	"strings"
	"time"

	"go.uber.org/zap"
)

type UserService struct {
	models.Users
	models.Emails
	models.Tokens
}

type UserInfo struct {
	Id            int       `json:"id"`
	Username      string    `json:"username"`
	PhotoLink     string    `json:"photo_link"`
	Name          string    `json:"name"`
	Role          string    `json:"role"`
	Membership    string    `json:"membership"`
	Gender        string    `json:"gender"`
	Country       string    `json:"country"`
	Organization  string    `json:"organization"`
	EmailId       int       `json:"email_id"`
	Email         string    `json:"email"`
	EmailVerified bool      `json:"email_verified"`
	Phone         string    `json:"phone"`
	Position      string    `json:"position"`
	Bio           string    `json:"bio"`
	Interest      string    `json:"interest"`
	Expertises    []string  `json:"expertise"`
	Paid          bool      `json:"paid"`
	CreateTime    time.Time `json:"create_time"`
}
type LoginResponse struct {
	Token    string   `json:"token"`
	UserInfo UserInfo `json:"user_info"`
}

func (s *UserService) GetUserInfo() (*UserInfo, error) {
	um := models.UserModel{}
	userInfo := models.UserInfo{
		Users: models.Users{Id: s.Users.Id},
	}
	user, err := um.GetUserInfo(&userInfo)
	if err != nil || user == nil {
		return nil, err
	}
	data := UserInfo{
		Id:            user.Users.Id,
		Username:      user.Username,
		PhotoLink:     user.AvatarUrl,
		Name:          user.Name,
		Role:          user.Role,
		Membership:    user.Membership,
		Gender:        user.Gender,
		Country:       user.Country,
		Organization:  user.Organization,
		EmailId:       user.EmailId,
		Email:         user.Email,
		EmailVerified: user.EmailVerified,
		Phone:         user.Phone,
		Position:      user.Position,
		Bio:           user.Bio,
		Interest:      user.Interest,
		Paid:          user.Paid,
		CreateTime:    user.CreateTime,
	}
	if user.Expertises == "" {
		data.Expertises = []string{}
	} else {
		data.Expertises = strings.Split(user.Expertises, ",")
	}

	return &data, nil
}

func (s *UserService) RegisterUser() error {
	em := models.EmailModel{}
	um := models.UserModel{}
	// email服务
	esc := email.GetEmailService()
	// 获取所需要的验证token和邮件link

	// 预备好email数据
	token, link := email.BuildVerifyEmailLink()
	emailRecord := models.Emails{
		Email:         s.Email,
		EmailVerified: false,
	}
	// 开启事务
	sess := models.NewTransaction()
	defer sess.CloseSession()
	sess.BeginTransaction()

	// 添加 email 数据，并返回email id
	emailId, err := em.AddEmail(&emailRecord)
	if err != nil {
		zap.S().Errorf("Failed to add email record: %v", err)
		sess.RollbackTransaction()
		return err
	}

	// 盐
	salt, err := bcrypt.GenerateSalt()
	if err != nil {
		zap.S().Errorf("Failed to generate salt: %v", err)
		sess.RollbackTransaction()
		return err
	}

	// 加密后的密码
	hashedPassword, err := bcrypt.HashPassword(s.Password, salt)
	if err != nil {
		zap.S().Errorf("Failed to hash password: %v", err)
		sess.RollbackTransaction()
		return err
	}

	// 更新上述更改的参数
	s.RoleId = models_field.RoleNormalUserId
	s.EmailId = emailId
	s.Password = hashedPassword
	s.Salt = salt
	s.Paid = false
	// 添加数据
	if um.AddUser(&s.Users,
		models_field.UserRoleId,
		models_field.UserMembershipId,
		models_field.UserEmailId,
		models_field.UserUsername,
		models_field.UserPassword,
		models_field.UserSalt,
		models_field.UserName,
		models_field.UserGender,
		models_field.UserOrganization,
		models_field.UserCountry,
		models_field.UserPaid,
		models_field.UserCreateTime) {
		zap.S().Infof("User %s added successfully", s.Username)

		ts := tokenService{}
		err = ts.AddTokenToDb(s.Users.Id, token)
		if err != nil {
			return err
		}
		policyService := PolicyService{}
		b, err := policyService.AssignRoleToUser(s.Users.Id, models_field.RoleNormalUser)
		zap.S().Infof("assign role %s to user %d: %v", models_field.RoleNormalUser, s.Users.Id, b)
		if err != nil {
			sess.RollbackTransaction()
			return err
		}
	} else {
		zap.S().Errorf("Failed to add user %s", s.Username)
		sess.RollbackTransaction()
		return errors.New("failed to add user")
	}
	sess.CommitTransaction()

	go func() {
		err = esc.SendVerificationEmail(s.Email, s.Username, link)
		if err != nil {
			zap.S().Errorf("Failed to send verification email to %s: %v", s.Email, err.Error())
		}
	}()

	return nil
}

func (s *UserService) Login() (*LoginResponse, error) {
	um := models.UserModel{}
	// Get user by username for login authentication
	user := um.GetUserForLogin(s.Username)
	// Verify password using bcrypt
	if user == nil || !bcrypt.CheckPassword(s.Password, user.Salt, user.Password) {
		return nil, errors.New("invalid username or password")
	}

	// Generate JWT token with email verification status
	token, err := jwt.GenerateToken(user.Users.Id, user.Username)
	if err != nil {
		return nil, err
	}

	return &LoginResponse{
		Token: token,
		UserInfo: UserInfo{
			Id:            user.Users.Id,
			Username:      user.Username,
			PhotoLink:     user.AvatarUrl,
			Name:          user.Name,
			Role:          user.Role,
			Membership:    user.Membership,
			Gender:        user.Gender,
			Country:       user.Country,
			Organization:  user.Organization,
			EmailId:       user.EmailId,
			Email:         user.Email,
			EmailVerified: user.EmailVerified,
			Phone:         user.Phone,
			Position:      user.Position,
			Bio:           user.Bio,
			Interest:      user.Interest,
			Expertises:    strings.Split(user.Expertises, ","),
			CreateTime:    user.CreateTime,
		},
	}, nil
}

func (s *UserService) FindPasswordStep1() error {
	um := models.UserModel{}
	// Get user by email
	user, err := um.GetUserByEmail(s.Email)
	if err != nil || user == nil {
		// 返回一个随机token，迷惑攻击者
		return errors.New("user not found for this email")
	}

	token, link := email.BuildResetPasswordLink()
	// Generate a random token for password reset

	// Save the token to the database
	ts := tokenService{}
	err = ts.AddTokenToDb(user.Users.Id, token)
	if err != nil {
		return err
	}

	// Send password reset email
	esc := email.GetEmailService()
	err = esc.SendPasswordResetEmail(s.Email, link)
	if err != nil {
		zap.S().Errorf("Failed to send password reset email to %s: %v", s.Email, err)
		return errors.New("failed to send password reset email")
	}
	zap.S().Infof("Password reset email sent to %s", s.Email)

	return nil
}

// FindPasswordStep2 validates the reset token and updates the password
func (s *UserService) FindPasswordStep2() error {
	tm := models.TokensModel{}
	um := models.UserModel{}
	// Check if token exists and is valid
	res, err := tm.GetToken(&models.Tokens{Token: s.Token})
	if err != nil {
		return err
	}
	if res == nil {
		return errors.New("invalid token")
	}

	// Check if token is expired
	if time.Now().After(res.ExpiresAt) {
		// Remove expired token
		err = tm.RemoveToken(res.Token)
		if err != nil {
			return err
		}
		return errors.New("token has expired")
	}

	// Get the user to retrieve their salt or generate a new one
	user, err := um.GetUserById(res.UserId)
	if err != nil || user == nil {
		return errors.New("user not found")
	}

	// Generate a new salt for better security
	salt, err := bcrypt.GenerateSalt()
	if err != nil {
		zap.S().Errorf("Failed to generate salt: %v", err)
		return errors.New("failed to process password update")
	}

	// Hash the new password with the salt
	hashedPassword, err := bcrypt.HashPassword(s.Password, salt)
	if err != nil {
		zap.S().Errorf("Failed to hash password: %v", err)
		return errors.New("failed to process password update")
	}

	// Update user record with new hashed password and salt
	updatedUser := models.Users{
		Id:       res.UserId,
		Password: hashedPassword,
		Salt:     salt,
	}

	// Update the user record
	err = um.UpdateUser(&updatedUser)
	if err != nil {
		return err
	}

	// Remove the used token
	err = tm.RemoveToken(res.Token)
	if err != nil {
		return err
	}

	return nil
}

func (s *UserService) UpdateUserProfile() error {
	um := models.UserModel{}
	// First check if user exists
	user := models.Users{Id: s.Users.Id}
	has, err := um.GetUser(&user)
	if err != nil {
		return err
	}
	if !has {
		return errors.New("can't find the user")
	}
	var cols []string
	if s.Expertises != user.Expertises {
		cols = append(cols, models_field.UserExpertises)
	}
	if s.Position != user.Position {
		cols = append(cols, models_field.UserPosition)
	}
	if s.Bio != user.Bio {
		cols = append(cols, models_field.UserBio)
	}
	if s.Interest != user.Interest {
		cols = append(cols, models_field.UserInterest)
	}
	if s.Organization != user.Organization {
		cols = append(cols, models_field.UserOrganization)
	}
	if len(cols) == 0 {
		return nil
	}

	return um.UpdateUser(&s.Users, cols...)
}

// UpdateUserAvatar 修改头像，需要有file content, size参数
func (s *UserService) UpdateUserAvatar(file *multipart.FileHeader) error {
	um := models.UserModel{}
	// 先获取file, 因为外面已经做过参数验证了，这个地方只关注业务逻辑
	opened, err := file.Open()
	if err != nil {
		zap.S().Errorf("Failed to open file: %v", err)
		return err
	}

	toByte, n, err := utils.ReadFileToByte(opened)
	if err != nil {
		zap.S().Error(err)
		return err
	}

	// 限制超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancel()

	filename := utils.GenerateRemoteFilename(file.Filename, s.Users.Id, consts.RemoteAvatarPath)
	// 上传到minio并获取minio返回的完整路径
	updateInfo, err := s3.UploadFileStream(ctx, filename, consts.HttpContentTypeWebP, toByte, int64(n))
	if err != nil {
		zap.S().Error(err)
		return err
	}
	zap.S().Debug(updateInfo)
	// 拼接完整的url + 路径

	// 保存到数据库
	err = um.UpdateUser(&models.Users{Id: s.Users.Id, AvatarUrl: updateInfo.Location}, models_field.UserAvatarUrl)
	if err != nil {
		return err
	}
	return nil
}

func (s *UserService) UpdatePassword() error {
	um := models.UserModel{}
	// First check if user exists
	user, err := um.GetUserById(s.Users.Id)
	if err != nil {
		return err
	}

	if user.Users.Id == 0 {
		return errors.New("user not found")
	}

	// Generate a new salt for better security
	salt, err := bcrypt.GenerateSalt()
	if err != nil {
		zap.S().Errorf("Failed to generate salt: %v", err)
		return errors.New("failed to process password update")
	}

	// Hash the new password with the salt
	hashedPassword, err := bcrypt.HashPassword(s.Password, salt)
	if err != nil {
		zap.S().Errorf("Failed to hash password: %v", err)
		return errors.New("failed to process password update")
	}

	// Update the user record
	_, err = um.UpdateUserPassword(s.Users.Id, hashedPassword, salt)
	if err != nil {
		zap.S().Errorf("Failed to update password: %v", err)
		return err
	}

	return nil
}

func (s *UserService) CheckUserPermission(userId int, obj string, act string) bool {
	policyService := PolicyService{}
	return policyService.CheckUserPermission(userId, obj, act)
}

func (s *UserService) GetUserRoles(userId int) []string {
	policyService := PolicyService{}
	return policyService.GetUserRoles(userId)
}

func (s *UserService) CheckExistUsername(username string) (bool, error) {
	um := models.UserModel{}
	return um.CheckUserExist(&models.Users{Username: username}, models_field.UserUsername)
}

func (s *UserService) CheckUserPaymentStatusByUsername(username string) (int, bool, error) {
	um := models.UserModel{}
	var user models.Users
	user.Username = username
	has, err := um.GetUser(&user, models_field.UserId, models_field.UserId, models_field.UserPaid)
	if err != nil {
		return 0, false, err
	}
	if !has {
		return 0, false, errors.New("user not found, check username again")
	}

	return user.Id, user.Paid, err
}
