package service

import (
	"next-meeting-backend/internal/app/models"
)

type AdminAbstractService struct {
}

func (s *AdminAbstractService) ListAbstracts(filter string, limit, offset int) (int64, []models.AbstractInfo, error) {
	am := models.AbstractModel{}

	m := map[string]interface{}{}
	count, abstracts, err := am.ListAbstracts(m, limit, offset)
	if err != nil {
		return 0, nil, err
	}
	return count, abstracts, nil
}

func (s *AdminAbstractService) Statistics() (int64, int64, int64, error) {
	m := map[string]interface{}{}
	am := models.AbstractModel{}
	flag := false
	total, err := am.CountAbstract(m)
	if err != nil {
		return 0, 0, 0, err
	}

	m["oral_presentation = ?"] = true
	oral, err := am.CountAbstract(m, &flag)
	if err != nil {
		return 0, 0, 0, err
	}
	m = make(map[string]interface{})
	m["poster_presentation = ?"] = true
	poster, err := am.CountAbstract(m, &flag)
	if err != nil {
		return 0, 0, 0, nil
	}
	return total, oral, poster, nil
}

func (s *AdminAbstractService) ExportAbstracts() error {
	am := models.AbstractModel{}
	_, abstracts, err := am.ListAbstracts(nil, 5000, 0)
	if err != nil {
		return err
	}

	for _, abstract := range abstracts {

		abstract.FilePath = "https://ifmb.maizemeeting.com/api/v1/abstracts/" + abstract.FilePath
	}

	return nil
}
