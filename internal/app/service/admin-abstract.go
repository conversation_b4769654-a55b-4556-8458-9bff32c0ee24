package service

import (
	"context"
	"fmt"
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/pkg/excel"
	"next-meeting-backend/internal/pkg/s3"
	"path/filepath"
	"strings"

	"go.uber.org/zap"
)

type AdminAbstractService struct {
}

func (s *AdminAbstractService) ListAbstracts(filter string, limit, offset int) (int64, []models.AbstractInfo, error) {
	am := models.AbstractModel{}

	m := map[string]interface{}{}
	count, abstracts, err := am.ListAbstracts(m, limit, offset)
	if err != nil {
		return 0, nil, err
	}
	return count, abstracts, nil
}

func (s *AdminAbstractService) Statistics() (int64, int64, int64, error) {
	m := map[string]interface{}{}
	am := models.AbstractModel{}
	flag := false
	total, err := am.CountAbstract(m)
	if err != nil {
		return 0, 0, 0, err
	}

	m["oral_presentation = ?"] = true
	oral, err := am.CountAbstract(m, &flag)
	if err != nil {
		return 0, 0, 0, err
	}
	m = make(map[string]interface{})
	m["poster_presentation = ?"] = true
	poster, err := am.CountAbstract(m, &flag)
	if err != nil {
		return 0, 0, 0, nil
	}
	return total, oral, poster, nil
}

// ExportAbstractsResult represents the result of abstract export
type ExportAbstractsResult struct {
	ExcelData []byte
	ZipData   []byte
	Filename  string
}

// ExportAbstracts exports abstracts data to Excel and downloads associated files as ZIP
func (s *AdminAbstractService) ExportAbstracts() (*ExportAbstractsResult, error) {
	ctx := context.Background()

	// 1. Get all abstracts from database
	am := models.AbstractModel{}
	_, abstracts, err := am.ListAbstracts(nil, 10000, 0) // Get all abstracts
	if err != nil {
		zap.S().Errorf("Failed to get abstracts from database: %v", err)
		return nil, fmt.Errorf("failed to get abstracts: %w", err)
	}

	if len(abstracts) == 0 {
		return nil, fmt.Errorf("no abstracts found")
	}

	zap.S().Infof("Found %d abstracts to export", len(abstracts))

	// 2. Prepare files for ZIP download first (before modifying paths)
	var filesToDownload []s3.FileDownloadInfo
	for _, abstract := range abstracts {
		if abstract.Abstracts.FilePath != "" && abstract.Abstracts.Filename != "" {
			filesToDownload = append(filesToDownload, s3.FileDownloadInfo{
				FilePath:     abstract.Abstracts.FilePath, // Use original path for downloading
				OriginalName: s.generateUniqueFilename(abstract.Abstracts.Filename, abstract.Abstracts.Id),
			})
		}
	}

	// 3. Generate Excel file with presigned URLs
	excelExporter := excel.NewExcelExporter()
	defer excelExporter.Close()

	// Update file paths to be downloadable URLs for Excel hyperlinks
	for i := range abstracts {
		if abstracts[i].FilePath != "" {
			// Generate presigned URL for Excel hyperlink
			url, err := s3.GetPresignedURL(ctx, abstracts[i].FilePath, abstracts[i].Abstracts.Id)
			if err != nil {
				zap.S().Warnf("Failed to generate presigned URL for abstract %d: %v", abstracts[i].Abstracts.Id, err)
				// Keep original path if presigned URL generation fails
			} else {
				abstracts[i].FilePath = url
			}
		}
	}

	excelData, err := excelExporter.CreateAbstractExportFile(abstracts)
	if err != nil {
		zap.S().Errorf("Failed to create Excel file: %v", err)
		return nil, fmt.Errorf("failed to create Excel file: %w", err)
	}

	zap.S().Info("Excel file created successfully")

	zap.S().Infof("Preparing to download %d files", len(filesToDownload))

	// 4. Generate filename
	filename := excel.GenerateAbstractExportFilename()

	// 5. Create ZIP with Excel file and abstract files
	var zipData []byte
	if len(filesToDownload) > 0 || len(excelData) > 0 {
		// Use the new function that includes Excel file in ZIP
		zipData, err = s3.CreateZipWithExcelAndFiles(ctx, filesToDownload, excelData, filename)
		if err != nil {
			zap.S().Errorf("Failed to create ZIP file with Excel: %v", err)
			// Don't fail the entire export if ZIP creation fails
			// Return Excel data without ZIP
			zap.S().Warn("Continuing with Excel export only")
		} else {
			zap.S().Infof("ZIP file created successfully with Excel and %d abstract files", len(filesToDownload))
		}
	}

	return &ExportAbstractsResult{
		ExcelData: excelData,
		ZipData:   zipData,
		Filename:  filename,
	}, nil
}

// generateUniqueFilename generates a unique filename to avoid conflicts in ZIP
func (s *AdminAbstractService) generateUniqueFilename(originalFilename string, abstractId int) string {
	if originalFilename == "" {
		return fmt.Sprintf("abstract_%d.pdf", abstractId)
	}

	// Extract file extension
	ext := filepath.Ext(originalFilename)
	nameWithoutExt := strings.TrimSuffix(originalFilename, ext)

	// Clean filename to avoid special characters
	nameWithoutExt = strings.ReplaceAll(nameWithoutExt, " ", "_")
	nameWithoutExt = strings.ReplaceAll(nameWithoutExt, "/", "_")
	nameWithoutExt = strings.ReplaceAll(nameWithoutExt, "\\", "_")

	// Add abstract ID to make it unique
	return fmt.Sprintf("%s_ID%d%s", nameWithoutExt, abstractId, ext)
}

// ExportAbstractsExcelOnly exports only Excel file without ZIP
func (s *AdminAbstractService) ExportAbstractsExcelOnly() ([]byte, string, error) {
	ctx := context.Background()

	// Get all abstracts from database
	am := models.AbstractModel{}
	_, abstracts, err := am.ListAbstracts(nil, 10000, 0)
	if err != nil {
		zap.S().Errorf("Failed to get abstracts from database: %v", err)
		return nil, "", fmt.Errorf("failed to get abstracts: %w", err)
	}

	if len(abstracts) == 0 {
		return nil, "", fmt.Errorf("no abstracts found")
	}

	// Generate presigned URLs for Excel hyperlinks
	for i := range abstracts {
		if abstracts[i].FilePath != "" {
			url, err := s3.GetPresignedURL(ctx, abstracts[i].FilePath, abstracts[i].Abstracts.Id)
			if err != nil {
				zap.S().Warnf("Failed to generate presigned URL for abstract %d: %v", abstracts[i].Abstracts.Id, err)
			} else {
				abstracts[i].FilePath = url
			}
		}
	}

	// Generate Excel file
	excelExporter := excel.NewExcelExporter()
	defer excelExporter.Close()

	excelData, err := excelExporter.CreateAbstractExportFile(abstracts)
	if err != nil {
		zap.S().Errorf("Failed to create Excel file: %v", err)
		return nil, "", fmt.Errorf("failed to create Excel file: %w", err)
	}

	filename := excel.GenerateAbstractExportFilename()
	return excelData, filename, nil
}
