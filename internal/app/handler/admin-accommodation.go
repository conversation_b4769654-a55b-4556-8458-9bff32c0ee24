package handler

import (
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"

	"github.com/gin-gonic/gin"
)

type AdminAccommodationHandler struct {
}

func (receiver *AdminAccommodationHandler) ListHotels(ctx *gin.Context) {
	aas := service.AdminAccommodationService{}
	hotels, err := aas.ListHotels()
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(ctx, hotels)
}

func (receiver *AdminAccommodationHandler) ListRooms(ctx *gin.Context) {
	aas := service.AdminAccommodationService{}
	rooms, err := aas.ListRooms()
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(ctx, rooms)

}

func (receiver *AdminAccommodationHandler) ListReservation(ctx *gin.Context) {
	aas := service.AdminAccommodationService{}
	count, reservations, err := aas.ListReservations()
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}

	utils.RespondWithSuccess(ctx, gin.H{
		"count": count,
		"list":  reservations,
	})

}

//type adminAccommodationStatisticsResponse struct {
//	TotalHotels    int64 `json:"total_hotels"`
//	TotalRooms     int64 `json:"total_rooms"`
//	AvailableRooms int64 `json:"available_rooms"`
//	WaitToAssign   int64 `json:"wait_to_assign"`
//}

//func (receiver *AdminAccommodationHandler) Statistics(ctx *gin.Context) {
//	aas := service.AdminAccommodationService{}
//	total, oral, poster, err := aas.Statistics()
//	if err != nil {
//		utils.RespondWithError(ctx, consts.Error, err.Error())
//		return
//	}
//	utils.RespondWithSuccess(ctx, gin.H{"total": total, "oral": oral, "poster": poster})
//}
