package handler

import (
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go.uber.org/zap"
)

type UserHandler struct {
}

const (
	AvatarMaxSize = 8 * 1024 * 1024
)

type RegisterData struct {
	RoleId       int    `form:"role" json:"role"`
	MembershipId int    `form:"membership" json:"membership" binding:"required"`
	Username     string `form:"username" json:"username" binding:"required,min=3,max=32"`
	Password     string `form:"password" json:"password" binding:"required,min=8"`
	Name         string `form:"name" json:"name" binding:"required,max=64"`
	Email        string `form:"email" json:"email" binding:"required,max=64,email"`
	Gender       string `form:"gender" json:"gender" binding:"required,oneof=male female"`
	Organization string `form:"organization" json:"organization" binding:"required,max=128"`
	Country      string `form:"country" json:"country" binding:"required,max=16"`
}

type LoginData struct {
	Username string `form:"username" json:"username" binding:"required"`
	Password string `form:"password" json:"password" binding:"required"`
}

type ForgotPasswordData struct {
	Email string `form:"email" json:"email" binding:"required,email"`
}

type ResetPasswordData struct {
	Token       string `form:"token" json:"token" binding:"required"`
	NewPassword string `form:"password" json:"password" binding:"required"`
}

type UpdatePasswordData struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required, min=8,max=32,eqfield=OldPassword"`
}

type UpdateUserData struct {
	Organization string   `json:"organization" binding:"max=128"`
	Biography    string   `json:"bio" binding:"max=2048"`
	Expertise    []string `json:"expertise"`
	Position     string   `json:"position"  binding:"max=32"`
	Interest     string   `json:"interest"  binding:"max=128"`
}

type TurnstileData struct {
	Token string `json:"token" binding:"required"`
}

type VerifyEmailData struct {
	Token string `form:"token" json:"token" binding:"required"`
}

type EmailRequest struct {
	UserId int    `form:"user_id" json:"user_id"`
	Email  string `form:"email" json:"email" binding:"required,email"`
}

func (h *UserHandler) LoginHandler(c *gin.Context) {
	var data LoginData
	if err := c.ShouldBindBodyWithJSON(&data); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	userService := service.UserService{
		Users: models.Users{
			Username: data.Username,
			Password: data.Password,
		},
	}

	response, err := userService.Login()
	if err != nil {
		utils.RespondWithError(c, consts.ErrorPasswordIncorrect)
		return
	}

	if response == nil {
		utils.RespondWithError(c, consts.ErrorPasswordIncorrect)
		return
	}
	utils.RespondWithSuccess(c, response, "Login successful")
}

func (h *UserHandler) RegisterHandler(c *gin.Context) {
	var userInfo RegisterData
	if err := c.ShouldBindBodyWith(&userInfo, binding.JSON); err != nil {
		zap.S().Errorf(err.Error())
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	userService := service.UserService{
		Users: models.Users{
			MembershipId: userInfo.MembershipId,
			Username:     userInfo.Username,
			Password:     userInfo.Password,
			Name:         userInfo.Name,
			Gender:       userInfo.Gender,
			Country:      userInfo.Country,
			Organization: userInfo.Organization,
			CreateTime:   time.Now(),
		}, Emails: models.Emails{
			Email: userInfo.Email,
		},
	}
	err := userService.RegisterUser()
	if err != nil {
		utils.RespondWithError(c, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(c, nil, "User registered successfully")
}

func (h *UserHandler) ForgotPasswordHandler(c *gin.Context) {
	var userInfo ForgotPasswordData
	if err := c.ShouldBindBodyWith(&userInfo, binding.JSON); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	userService := service.UserService{
		Emails: models.Emails{
			Email: userInfo.Email,
		},
	}

	err := userService.FindPasswordStep1()
	if err != nil {
		// 找不到也不能返回错误。
		zap.S().Debug("can't find the email: ", userInfo.Email)
		utils.RespondWithSuccess(c, nil, "Password reset instructions sent to your email")
		return
	}
	zap.S().Debug("Password reset instructions sent to your email", userInfo.Email)
	utils.RespondWithSuccess(c, nil, "Password reset instructions sent to your email")
}

// ResetPasswordHandler handles the password reset with token
func (h *UserHandler) ResetPasswordHandler(c *gin.Context) {
	var resetData ResetPasswordData
	if err := c.ShouldBindBodyWith(&resetData, binding.JSON); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}
	s := service.UserService{}

	s.Token = resetData.Token
	s.Password = resetData.NewPassword
	err := s.FindPasswordStep2()
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to reset password")
		return
	}
	utils.RespondWithSuccess(c, nil, "Password reset successfully")
}

// VerifyEmailHandler handles email verification
func (h *UserHandler) VerifyEmailHandler(c *gin.Context) {
	var data VerifyEmailData
	if err := c.ShouldBindBodyWith(&data, binding.JSON); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	e := service.EmailService{}
	err := e.VerifyEmail(data.Token)
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to verify email")
		return
	}

	utils.RespondWithSuccess(c, nil, "Email verified successfully")
}

// ResendVerificationHandler resends the verification email
func (h *UserHandler) ResendVerificationHandler(c *gin.Context) {
	var data EmailRequest
	if err := c.ShouldBindBodyWithJSON(&data); err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid email format")
		return
	}

	e := service.EmailService{}
	err := e.ResendVerificationEmail(data.Email)
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to resend verification email")
		return
	}

	utils.RespondWithSuccess(c, nil, "Verification email has been resent")
}

// GetUserHandler returns information for a specific user
func (h *UserHandler) GetUserHandler(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid user ID")
		return
	}

	s := service.UserService{
		Users: models.Users{Id: userId},
	}

	user, err := s.GetUserInfo()
	if &user == nil {
		utils.RespondWithError(c, consts.ErrorUserNotFound)
		return
	}

	utils.RespondWithSuccess(c, user, "get user successfully")
}

// UpdateUserHandler updates user information
func (h *UserHandler) UpdateUserHandler(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	var userData UpdateUserData
	if err = c.ShouldBindBodyWithJSON(&userData); err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid user data")
		return
	}

	s := service.UserService{
		Users: models.Users{
			Id:           userId,
			Organization: userData.Organization,
			Bio:          userData.Biography,
			Position:     userData.Position,
			Interest:     userData.Interest,
			Expertises:   strings.Join(userData.Expertise, ","),
		},
	}

	if err = s.UpdateUserProfile(); err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to update user")
		return
	}

	utils.RespondWithSuccess(c, nil, "User updated successfully")
}

// UpdatePasswordHandler updates a user's password
func (h *UserHandler) UpdatePasswordHandler(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid user ID")
		return
	}

	var passwordData UpdatePasswordData
	if err = c.ShouldBindBodyWithJSON(&passwordData); err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid password data")
		return
	}

	s := service.UserService{
		Users: models.Users{
			Id:       userId,
			Password: passwordData.NewPassword,
		},
	}

	if err = s.UpdatePassword(); err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to update password")
		return
	}
	utils.RespondWithSuccess(c, nil, "Password updated successfully")
}

func (h *UserHandler) UpdateUserAvatarHandler(c *gin.Context) {
	form, _ := c.MultipartForm()
	// 验证文件参数
	fileParams := form.File["avatar"]
	avatar := fileParams[0]
	if avatar == nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}
	// 验证文件大小
	if avatar.Size > AvatarMaxSize {
		utils.RespondWithError(c, consts.ErrorUploadFileSizeLimitExceeded)
		return
	}

	// 验证文件类型
	contentType := avatar.Header.Get("content-type")
	if !strings.HasPrefix(contentType, "image/") {
		utils.RespondWithError(c, consts.ErrorUploadFileTypeNotAllowed)
		return
	}

	userService := service.UserService{}
	err := userService.UpdateUserAvatar(avatar)
	if err != nil {
		zap.S().Errorf("failed to update user's avatar: %v", err)
		utils.RespondWithError(c, consts.Error, "file upload failed")
		return
	}
	utils.RespondWithSuccess(c, nil, "update successfully")
}

func (h *UserHandler) UpdateUserEmailHandler(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}
	emailData := EmailRequest{}
	if err = c.ShouldBindBodyWithJSON(&emailData); err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid email format")
		return
	}

	e := service.EmailService{}
	err = e.UpdateUserEmail(userId, emailData.Email)
	if err != nil {
		utils.RespondWithError(c, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(c, nil, "Email updated successfully")
}

func (h *UserHandler) CheckExistUsername(c *gin.Context) {
	username := c.Query("username")
	if username == "" {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid username")
		return
	}
	s := service.UserService{}
	exist, err := s.CheckExistUsername(username)
	if err != nil {
		zap.S().Errorf("CheckExistUsername failed: %v", err)
		utils.RespondWithError(c, consts.Error, "The username is not found, please check again")
		return
	}
	utils.RespondWithSuccess(c, gin.H{"exist": exist}, "Check username successfully")
}

func (h *UserHandler) CheckUserPaymentStatusByUsername(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid username")
		return
	}
	s := service.UserService{}
	id, paid, err := s.CheckUserPaymentStatusByUsername(username)
	if err != nil {
		zap.S().Errorf("CheckExistUsername failed: %v", err)
		utils.RespondWithError(c, consts.Success, "The username is not found, please check again")
		return
	}
	utils.RespondWithSuccess(c, gin.H{"id": id, "paid": paid}, "Check username successfully")
}
