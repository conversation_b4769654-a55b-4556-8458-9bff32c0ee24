package handler

import (
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/models_field"
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type AbstractHandler struct {
}

type SubmitAbstractRequest struct {
	Title              string `form:"title" binding:"required"`
	Theme              int    `form:"theme_id" binding:"required"`
	PublicationStatus  string `form:"publication_status" binding:"required,oneof=unpublished partially published"`
	OralPresentation   bool   `form:"oral_presentation"`
	PosterPresentation bool   `form:"poster_presentation"`
}

func (h *AbstractHandler) SubmitAbstract(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "unknown user")
		return
	}
	var abstractData SubmitAbstractRequest
	err = c.Should<PERSON>(&abstractData, binding.Form)
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "invalid abstract data")
		return
	}

	file, fileHeader, err := c.Request.FormFile("abstract")
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "no receipt file")
		return
	}

	if fileHeader.Size > int64(consts.AbstractMaxSize) {
		utils.RespondWithError(c, consts.ErrorUploadFileSizeLimitExceeded)
		return
	}

	as := service.AbstractService{
		Abstracts: models.Abstracts{
			UserId:             userId,
			Title:              abstractData.Title,
			Theme:              abstractData.Theme,
			PublicationStatus:  models_field.AbstractPublishStatus(abstractData.PublicationStatus),
			OralPresentation:   abstractData.OralPresentation,
			PosterPresentation: abstractData.PosterPresentation,
		},
	}

	err = as.SubmitAbstract(file, fileHeader)
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to submit abstract")
		return
	}
	utils.RespondWithSuccess(c, nil, "Abstract submitted successfully")
}

func (h *AbstractHandler) UpdateAbstractHandler(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "unknown id")
		return
	}

	var abstractData SubmitAbstractRequest
	err = c.ShouldBindWith(&abstractData, binding.Form)
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "invalid abstract data")
		return
	}

	file, fileHeader, err := c.Request.FormFile("abstract")
	if err != nil {
		file = nil
		fileHeader = nil
	}

	if fileHeader == nil || fileHeader.Size > int64(consts.AbstractMaxSize) {
		utils.RespondWithError(c, consts.ErrorUploadFileSizeLimitExceeded)
		return
	}

	as := service.AbstractService{
		Abstracts: models.Abstracts{
			Id:                 id,
			Title:              abstractData.Title,
			Theme:              abstractData.Theme,
			PublicationStatus:  models_field.AbstractPublishStatus(abstractData.PublicationStatus),
			OralPresentation:   abstractData.OralPresentation,
			PosterPresentation: abstractData.PosterPresentation,
		},
	}

	err = as.UpdateAbstract(file, fileHeader)
	if err != nil {
		utils.RespondWithError(c, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(c, nil, "Abstract updated successfully")

}

func (h *AbstractHandler) GetAbstract(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "unknown user")
		return
	}
	svc := service.AbstractService{
		Abstracts: models.Abstracts{
			UserId: userId,
		},
	}
	abstract, err := svc.GetAbstract()
	if err != nil {
		utils.RespondWithError(c, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(c, abstract, "get abstract successfully")
}
