package handler

import (
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"strconv"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

// PolicyData represents the data needed for policy operations
type PolicyData struct {
	Role   string `json:"role" binding:"required"`
	Object string `json:"object" binding:"required"`
	Action string `json:"action" binding:"required"`
}

// RoleAssignmentData represents the data needed for role assignment
type RoleAssignmentData struct {
	UserId int    `json:"user_id" binding:"required"`
	Role   string `json:"role" binding:"required"`
}

// AddPolicyHandler adds a new policy rule
func AddPolicyHandler(c *gin.Context) {
	var data PolicyData
	if err := c.ShouldBindJSON(&data); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	success := service.AddPermissionForRole(data.Role, data.Object, data.Action)
	if !success {
		utils.RespondWithError(c, consts.Error, "Failed to add policy")
		return
	}

	utils.RespondWithSuccess(c, nil, "Policy added successfully")
}

// RemovePolicyHandler removes a policy rule
func RemovePolicyHandler(c *gin.Context) {
	var data PolicyData
	if err := c.ShouldBindJSON(&data); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	success := service.RemovePermissionForRole(data.Role, data.Object, data.Action)
	if !success {
		utils.RespondWithError(c, consts.Error, "Failed to remove policy")
		return
	}

	utils.RespondWithSuccess(c, nil, "Policy removed successfully")
}

// AssignRoleHandler assigns a role to a user
func AssignRoleHandler(c *gin.Context) {
	var data RoleAssignmentData
	if err := c.ShouldBindJSON(&data); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	policyService := service.PolicyService{}

	success, err := policyService.AssignRoleToUser(data.UserId, data.Role)
	zap.S().Debugw("success", "success", success)
	if err != nil {
		zap.S().Debug(err)
		utils.RespondWithError(c, consts.Error, "Failed to assign role")
		return
	}

	utils.RespondWithSuccess(c, nil, "Role assigned successfully")
}

// RemoveRoleHandler removes a role from a user
func RemoveRoleHandler(c *gin.Context) {
	var data RoleAssignmentData
	if err := c.ShouldBindJSON(&data); err != nil {
		utils.RespondWithError(c, consts.InvalidParams)
		return
	}

	policyService := service.PolicyService{}

	success := policyService.RemoveRoleFromUser(data.UserId, data.Role)
	if !success {
		utils.RespondWithError(c, consts.Error, "Failed to remove role")
		return
	}

	utils.RespondWithSuccess(c, nil, "Role removed successfully")
}

// GetUserRolesHandler gets all roles for a user
func GetUserRolesHandler(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "Invalid user ID")
		return
	}

	policyService := service.PolicyService{}
	roles := policyService.GetUserRoles(userId)
	utils.RespondWithSuccess(c, roles, consts.GetMsg(consts.Success))
}

// InitializeBasicPoliciesHandler initializes the basic policies for the application
func InitializeBasicPoliciesHandler(c *gin.Context) {
	service.InitializeBasicPolicies()

	utils.RespondWithSuccess(c, nil, "Basic policies initialized successfully")
}
