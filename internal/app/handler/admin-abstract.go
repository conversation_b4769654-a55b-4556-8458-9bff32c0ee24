package handler

import (
	"fmt"
	"net/http"
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AdminAbstractHandler struct {
}

type adminListAbstractsRequest struct {
	Filter string `form:"filter"`
	Limit  int    `form:"limit"`
	Offset int    `form:"offset"`
}

type adminListAbstractsResponse struct {
	Abstracts []models.AbstractInfo `json:"abstracts"`
	Total     int64                 `json:"total"`
}

func (receiver *AdminAbstractHandler) ListAbstracts(ctx *gin.Context) {
	var req adminListAbstractsRequest
	if err := ctx.ShouldBind(&req); err != nil {
		utils.RespondWithError(ctx, consts.InvalidParams, err.Error())
		return
	}

	aas := service.AdminAbstractService{}
	i, abstracts, err := aas.ListAbstracts(req.Filter, req.Limit, req.Offset)
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}
	resp := adminListAbstractsResponse{
		Total:     i,
		Abstracts: abstracts,
	}
	utils.RespondWithSuccess(ctx, resp)
}

func (receiver *AdminAbstractHandler) Statistics(ctx *gin.Context) {
	aas := service.AdminAbstractService{}
	total, oral, poster, err := aas.Statistics()
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(ctx, gin.H{"total": total, "oral": oral, "poster": poster})
}

// ExportAbstracts exports abstracts data to Excel with associated files as ZIP
func (receiver *AdminAbstractHandler) ExportAbstracts(ctx *gin.Context) {
	// Get export type from query parameter (default: "both")
	exportType := ctx.DefaultQuery("type", "both") // "excel", "zip", "both"

	aas := service.AdminAbstractService{}

	switch exportType {
	case "excel":
		receiver.exportExcelOnly(ctx, &aas)
	case "both":
		receiver.exportBoth(ctx, &aas)
	default:
		utils.RespondWithError(ctx, consts.InvalidParams, "Invalid export type. Use 'excel' or 'both'")
	}
}

// exportExcelOnly exports only Excel file
func (receiver *AdminAbstractHandler) exportExcelOnly(ctx *gin.Context, aas *service.AdminAbstractService) {
	excelData, filename, err := aas.ExportAbstractsExcelOnly()
	if err != nil {
		zap.S().Errorf("Failed to export abstracts Excel: %v", err)
		utils.RespondWithError(ctx, consts.Error, "Failed to export abstracts")
		return
	}

	// Set headers for Excel file download
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	ctx.Header("Content-Length", strconv.Itoa(len(excelData)))

	// Write Excel data
	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelData)
}

// exportBoth exports both Excel and ZIP files
func (receiver *AdminAbstractHandler) exportBoth(ctx *gin.Context, aas *service.AdminAbstractService) {
	result, err := aas.ExportAbstracts()
	if err != nil {
		zap.S().Errorf("Failed to export abstracts: %v", err)
		utils.RespondWithError(ctx, consts.Error, "Failed to export abstracts")
		return
	}

	// If ZIP data is available, return ZIP containing both Excel and files
	if len(result.ZipData) > 0 {
		receiver.returnZipWithExcelAndFiles(ctx, result)
	} else {
		// If no ZIP data (no files to download), return Excel only
		receiver.returnExcelOnly(ctx, result)
	}
}

// returnZipWithExcelAndFiles returns a ZIP file containing Excel and abstract files
func (receiver *AdminAbstractHandler) returnZipWithExcelAndFiles(ctx *gin.Context, result *service.ExportAbstractsResult) {
	// The ZIP already contains both Excel file and abstract files in abstracts/ subdirectory
	timestamp := time.Now().Format("20060102_150405")
	zipFilename := fmt.Sprintf("abstracts_export_%s.zip", timestamp)

	ctx.Header("Content-Type", "application/zip")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", zipFilename))
	ctx.Header("Content-Length", strconv.Itoa(len(result.ZipData)))

	zap.S().Infof("Returning ZIP file with Excel and abstract files: %s (%d bytes)", zipFilename, len(result.ZipData))
	ctx.Data(http.StatusOK, "application/zip", result.ZipData)
}

// returnExcelOnly returns only the Excel file when no ZIP is available
func (receiver *AdminAbstractHandler) returnExcelOnly(ctx *gin.Context, result *service.ExportAbstractsResult) {
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", result.Filename))
	ctx.Header("Content-Length", strconv.Itoa(len(result.ExcelData)))

	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", result.ExcelData)
}
