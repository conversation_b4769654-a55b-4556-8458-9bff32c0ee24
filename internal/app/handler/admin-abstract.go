package handler

import (
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"

	"github.com/gin-gonic/gin"
)

type AdminAbstractHandler struct {
}

type adminListAbstractsRequest struct {
	Filter string `form:"filter"`
	Limit  int    `form:"limit"`
	Offset int    `form:"offset"`
}

type adminListAbstractsResponse struct {
	Abstracts []models.AbstractInfo `json:"abstracts"`
	Total     int64                 `json:"total"`
}

func (receiver *AdminAbstractHandler) ListAbstracts(ctx *gin.Context) {
	var req adminListAbstractsRequest
	if err := ctx.ShouldBind(&req); err != nil {
		utils.RespondWithError(ctx, consts.InvalidParams, err.Error())
		return
	}

	aas := service.AdminAbstractService{}
	i, abstracts, err := aas.ListAbstracts(req.Filter, req.Limit, req.Offset)
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}
	resp := adminListAbstractsResponse{
		Total:     i,
		Abstracts: abstracts,
	}
	utils.RespondWithSuccess(ctx, resp)
}

func (receiver *AdminAbstractHandler) Statistics(ctx *gin.Context) {
	aas := service.AdminAbstractService{}
	total, oral, poster, err := aas.Statistics()
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(ctx, gin.H{"total": total, "oral": oral, "poster": poster})
}
