package handler

import (
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type AccommodationHandler struct {
}

type ReverseRoomRequest struct {
	RoomId        int    `json:"room_id" form:"room_id" binding:"required"`
	SharedOption  int    `json:"shared_option" form:"shared_option" binding:"required"`
	Occupant      int    `json:"occupant" form:"occupant" binding:"required"`
	CheckinDate   string `json:"checkin_date" form:"checkin_date" binding:"required"`
	CheckoutDate  string `json:"checkout_date" form:"checkout_date" binding:"required"`
	PartnerUserId int    `json:"partner_user_id" form:"partner_user_id"`
}

func (a *AccommodationHandler) GetHotels(c *gin.Context) {
	svc := service.AccommodationService{}
	hotels, err := svc.GetHotelRooms()
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to get hotels")
		return
	}
	utils.RespondWithSuccess(c, hotels, "get hotels successfully")
}

func (a *AccommodationHandler) ReverseRoom(c *gin.Context) {
	// 获取预定信息
	var roomData ReverseRoomRequest
	if err := c.ShouldBindBodyWithJSON(&roomData); err != nil {
		utils.RespondWithError(c, consts.InvalidParams, err.Error())
		return
	}

	checkinDate, err := time.Parse("2006-01-02", roomData.CheckinDate)
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "invalid checkin date")
		return
	}
	checkoutDate, err := time.Parse("2006-01-02", roomData.CheckoutDate)
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, "invalid checkout date")
		return
	}

	svc := service.AccommodationService{
		RoomReservations: models.RoomReservations{
			RoomId:       roomData.RoomId,
			SharedOption: roomData.SharedOption,
			UserId:       roomData.Occupant,
			CheckinDate:  checkinDate,
			CheckoutDate: checkoutDate,
			CreatedTime:  time.Now(),
		},
		PartnerUserId: roomData.PartnerUserId,
	}
	err = svc.AddAccommodations()
	if err != nil {
		utils.RespondWithError(c, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(c, nil, "Room reservation reversed successfully")

}

func (a *AccommodationHandler) GetMyAccommodations(c *gin.Context) {
	userId, _ := c.GetQuery("user_id")
	id, err := strconv.Atoi(userId)
	if err != nil {
		utils.RespondWithError(c, consts.InvalidParams, err.Error())
		return
	}
	svc := service.AccommodationService{}
	roomReservationInfo, err := svc.GetMyReservation(id)
	if err != nil {
		utils.RespondWithError(c, consts.Error, "Failed to get room reservation: ", err.Error())
		return
	}

	if roomReservationInfo == (models.RoomReservationInfo{}) {
		utils.RespondWithSuccess(c, nil, "room reservation not found")
		return
	}
	utils.RespondWithSuccess(c, roomReservationInfo, "get room reservation successfully")
}

func (a *AccommodationHandler) CancelRoomReservation(c *gin.Context) {}

func (a *AccommodationHandler) CheckUserReservation(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		utils.RespondWithError(c, consts.InvalidParams, "unknown user")
		return
	}

	svc := service.AccommodationService{}
	uid, err := svc.CheckUserReservation(username)
	if err != nil {
		utils.RespondWithError(c, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(c, gin.H{"user_id": uid}, "check user reservation successfully")
}
