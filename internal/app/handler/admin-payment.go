package handler

import (
	"next-meeting-backend/internal/app/models"
	"next-meeting-backend/internal/app/service"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go.uber.org/zap"
)

type AdminPaymentHandler struct {
}

type AdminListPaymentsRequest struct {
	Limit  int    `form:"limit" json:"limit"`
	Offset int    `form:"offset" json:"offset"`
	Filter string `form:"filter" json:"filter"`
	Status string `form:"status" json:"status"`
}

func (receiver *AdminPaymentHandler) ListPayments(ctx *gin.Context) {
	var r AdminListPaymentsRequest
	if err := ctx.ShouldBind(&r); err != nil {
		zap.S().Error(err)
		utils.RespondWithError(ctx, consts.InvalidParams, err.Error())
		return
	}

	paymentService := service.PaymentService{}
	transactions, count, err := paymentService.ListPayments(r.<PERSON>it, r.Offset, r.Filter, r.Status)
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}
	data := gin.H{"transactions": transactions, "total": count}
	utils.RespondWithSuccess(ctx, data, "list payments successfully")
}

func (receiver *AdminPaymentHandler) GetPaymentEvidenceUrl(ctx *gin.Context) {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		utils.RespondWithError(ctx, consts.InvalidParams, "unknown id")
		return
	}

	paymentService := service.PaymentService{}
	evidenceUrl, err := paymentService.GetEvidence(id)
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}
	utils.RespondWithSuccess(ctx, gin.H{"evidence": evidenceUrl}, "get payment evidence successfully")
}

type ReviewRequest struct {
	Reviewer int  `form:"reviewer" json:"reviewer"`
	Approved bool `form:"approved" json:"approved"`
}

// AddReview 添加审核记录
func (receiver *AdminPaymentHandler) AddReview(ctx *gin.Context) {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		utils.RespondWithError(ctx, consts.InvalidParams, "unknown id")
		return
	}
	var reviewData ReviewRequest
	if err = ctx.ShouldBindBodyWith(&reviewData, binding.JSON); err != nil {
		zap.S().Errorf("Failed to bind review data: %v", err)
		utils.RespondWithError(ctx, consts.InvalidParams, err.Error())
		return
	}

	svc := service.PaymentService{
		Reviews: models.Reviews{
			Tid:        id,
			Reviewer:   reviewData.Reviewer,
			Approved:   reviewData.Approved,
			ReviewTime: time.Now(),
		}}
	err = svc.AddReview()
	if err != nil {
		utils.RespondWithError(ctx, consts.Error, err.Error())
		return
	}

	utils.RespondWithSuccess(ctx, nil, "review added successfully")
}
