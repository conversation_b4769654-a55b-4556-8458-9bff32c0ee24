package routers

import (
	"next-meeting-backend/internal/app/handler"
	"next-meeting-backend/internal/middleware/jwt"

	"github.com/gin-gonic/gin"
)

// PaymentRoutesRegister 注册支付相关路由
func PaymentRoutesRegister(r *gin.Engine) {
	paymentHandler := &handler.PaymentHandler{}

	// 支付API组
	paymentGroup := r.Group("/api")
	paymentGroup.Use(jwt.JWT())
	{
		// 创建支付记录
		paymentGroup.POST("/payment/:user_id", paymentHandler.CreatePayment)

		// 获取用户自己的支付记录列表
		//paymentGroup.GET("/payment/user", paymentHandler.ListUserPayments)

		// 获取单个支付记录详情
		paymentGroup.GET("/payment/:user_id", paymentHandler.GetPayment)

		// 更新支付记录
		paymentGroup.PUT("/payment/:id", paymentHandler.UpdatePayment)

		// 新增发票信息
		paymentGroup.POST("/payment/invoice/:user_id", paymentHandler.AddInvoiceInfo)

		paymentGroup.PUT("/payment/invoice/:id", paymentHandler.UpdateInvoiceInfo)

		// 获取发票信息
		paymentGroup.GET("/payment/invoice/:user_id", paymentHandler.GetInvoiceInfo)

		// 删除支付记录
		//paymentGroup.DELETE("/payment/:id", paymentHandler.DeletePayment)
	}
}
