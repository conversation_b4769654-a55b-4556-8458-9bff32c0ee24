package routers

import (
	"next-meeting-backend/internal/app/handler"
	"next-meeting-backend/internal/middleware/casbin"
	"next-meeting-backend/internal/middleware/jwt"

	"github.com/gin-gonic/gin"
)

func AdminRouteRegister(e *gin.Engine) {
	var payment handler.AdminPaymentHandler
	var user handler.AdminUserHandler
	var abstract handler.AdminAbstractHandler
	var accommodation handler.AdminAccommodationHandler

	admin := e.Group("/api/admin")
	admin.Use(jwt.JWT())
	admin.Use(casbin.AuthorizeByRole("admin"))
	{
		admin.GET("/users", user.AdminListUsersHandler)
		admin.PUT("/users/:id", user.AdminUpdateUserHandler)
		admin.DELETE("/users/:id", user.AdminDeleteUserHandler)
		admin.POST("/users", user.AdminAddUserHandler)

		admin.GET("/payments", payment.ListPayments)
		admin.GET("/payments/:id/evidence", payment.GetPaymentEvidenceUrl)
		admin.POST("/payment/review/:id", payment.AddReview)

		admin.GET("/abstracts", abstract.ListAbstracts)
		admin.GET("/abstract/statistics", abstract.Statistics)

		admin.GET("/accommodation/hotels", accommodation.ListHotels)
		admin.GET("/accommodation/rooms", accommodation.ListRooms)
		admin.GET("/accommodation/reservations", accommodation.ListReservation)
	}
}
