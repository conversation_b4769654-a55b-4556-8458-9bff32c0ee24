package models

import (
	"next-meeting-backend/internal/app/models_field"
	"time"

	"go.uber.org/zap"
)

type AbstractModel struct {
	Users
	Abstracts
	Themes
}

type Abstracts struct {
	Id                 int                                `json:"abstract_id" xorm:"not null pk autoincr"`
	UserId             int                                `json:"user_id"`
	Title              string                             `json:"title"`
	Theme              int                                `json:"theme_id"`
	PublicationStatus  models_field.AbstractPublishStatus `json:"publication_status"`
	OralPresentation   bool                               `json:"oral_presentation"`
	PosterPresentation bool                               `json:"poster_presentation"`
	FilePath           string                             `json:"abstract_path"`
	Filename           string                             `json:"filename"`
	CreateTime         time.Time                          `json:"submit_time" xorm:"created"`
	UpdateTime         time.Time                          `json:"update_time" xorm:"updated"`
}

type AbstractInfo struct {
	Abstracts    `xorm:"extends"`
	Themes       `xorm:"extends"`
	UserBaseInfo `xorm:"extends"`
}

type Themes struct {
	Id   int    `json:"theme_id" xorm:"not null pk autoincr"`
	Name string `json:"theme"`
}

func (m *AbstractModel) GetAbstract(cond string, args interface{}, cols ...string) (res *AbstractInfo, err error) {
	info := new(AbstractInfo)
	b, err := buildOrmCols(getExecutor(), cols...).Table("abstracts").Alias("a").
		Join("INNER", []string{"themes", "t"}, "t.id = a.theme").
		Join("INNER", []string{"users", "u"}, "u.id = a.user_id").
		Where(cond, args).Get(info)
	if err != nil {
		return nil, err
	}
	if !b {
		return nil, nil
	}
	return info, nil
}

func (m *AbstractModel) ListAbstracts(filter map[string]interface{}, limit, offset int) (count int64, abstracts []AbstractInfo, err error) {
	count, err = appendParamsOr(getExecutor(), filter).Table("abstracts").Alias("a").
		Join("INNER", []string{"themes", "t"}, "t.id = a.theme").
		Join("INNER", []string{"users", "u"}, "u.id = a.user_id").Limit(limit, offset).
		FindAndCount(&abstracts)
	if err != nil {
		zap.S().Error(err)
		return count, nil, nil
	}
	return count, abstracts, nil
}

func (m *AbstractModel) CountAbstract(filter map[string]interface{}, flag ...*bool) (count int64, err error) {
	count, err = appendParamsOr(getExecutor(), filter, flag...).Table("abstracts").Alias("a").
		Join("INNER", []string{"themes", "t"}, "t.id = a.theme").
		Join("INNER", []string{"users", "u"}, "u.id = a.user_id").Count(&Abstracts{})
	if err != nil {
		zap.S().Error("count users error!! ", err.Error())
		return 0, err
	}
	return count, nil
}

func (m *AbstractModel) AddAbstract(abstract *Abstracts, cols ...string) (int, error) {
	sess := buildOrmCols(getExecutor(), cols...)
	res, err := sess.Insert(abstract)
	if err != nil {
		return 0, err
	}

	if res <= 0 {
		return 0, nil
	}

	return abstract.Id, nil
}

func (m *AbstractModel) UpdateAbstract(abstract *Abstracts, cols ...string) (int, error) {
	sess := buildOrmCols(getExecutor(), cols...)
	res, err := sess.ID(abstract.Id).Update(abstract)
	if err != nil {
		return 0, err
	}

	if res <= 0 {
		return 0, nil
	}

	return abstract.Id, nil
}
