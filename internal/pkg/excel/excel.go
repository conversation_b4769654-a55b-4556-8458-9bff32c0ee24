package excel

import (
	"fmt"
	"next-meeting-backend/internal/app/models"
	"time"

	"github.com/xuri/excelize/v2"
)

// AbstractExportData represents the data structure for abstract export
type AbstractExportData struct {
	ID                 int    `json:"id"`
	UserName           string `json:"user_name"`
	Username           string `json:"username"`
	Email              string `json:"email"`
	Organization       string `json:"organization"`
	Country            string `json:"country"`
	Gender             string `json:"gender"`
	Title              string `json:"title"`
	Theme              string `json:"theme"`
	PublicationStatus  string `json:"publication_status"`
	OralPresentation   bool   `json:"oral_presentation"`
	PosterPresentation bool   `json:"poster_presentation"`
	Filename           string `json:"filename"`
	FilePath           string `json:"file_path"`
	SubmitTime         string `json:"submit_time"`
}

// ExcelExporter handles Excel file generation
type ExcelExporter struct {
	file *excelize.File
}

// NewExcelExporter creates a new Excel exporter
func NewExcelExporter() *ExcelExporter {
	return &ExcelExporter{
		file: excelize.NewFile(),
	}
}

// CreateAbstractExportFile creates an Excel file with abstract data
func (e *ExcelExporter) CreateAbstractExportFile(abstracts []models.AbstractInfo) ([]byte, error) {
	sheetName := "Abstracts"

	// Create new sheet
	index, err := e.file.NewSheet(sheetName)
	if err != nil {
		return nil, fmt.Errorf("failed to create sheet: %w", err)
	}

	// Set active sheet
	e.file.SetActiveSheet(index)

	// Delete default sheet
	e.file.DeleteSheet("Sheet1")

	// Set headers (English)
	headers := []string{
		"ID", "User Name", "Username", "Email", "Organization", "Country", "Gender",
		"Title", "Theme", "Publication Status", "Oral Presentation", "Poster Presentation",
		"Filename", "File Path", "Submit Time",
	}

	// Write headers
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		if err := e.file.SetCellValue(sheetName, cell, header); err != nil {
			return nil, fmt.Errorf("failed to set header: %w", err)
		}
	}

	// Style headers
	if err := e.styleHeaders(sheetName, len(headers)); err != nil {
		return nil, fmt.Errorf("failed to style headers: %w", err)
	}

	// Write data
	for i, abstract := range abstracts {
		row := i + 2 // Start from row 2 (after headers)

		// Convert data
		data := []interface{}{
			abstract.Abstracts.Id,
			abstract.UserBaseInfo.Name,
			abstract.UserBaseInfo.Username,
			"", // Email will be filled if available
			abstract.UserBaseInfo.Organization,
			abstract.UserBaseInfo.Country,
			abstract.UserBaseInfo.Gender,
			abstract.Abstracts.Title,
			abstract.Themes.Name,
			string(abstract.Abstracts.PublicationStatus),
			abstract.Abstracts.OralPresentation,
			abstract.Abstracts.PosterPresentation,
			abstract.Abstracts.Filename,
			abstract.Abstracts.FilePath,
			abstract.Abstracts.CreateTime.Format("2006-01-02 15:04:05"),
		}

		// Write row data
		for j, value := range data {
			cell := fmt.Sprintf("%s%d", string(rune('A'+j)), row)
			if err := e.file.SetCellValue(sheetName, cell, value); err != nil {
				return nil, fmt.Errorf("failed to set cell value: %w", err)
			}
		}

		// Create hyperlink for filename if file path exists
		if abstract.Abstracts.FilePath != "" && abstract.Abstracts.Filename != "" {
			filenameCell := fmt.Sprintf("M%d", row) // Column M is Filename
			if err := e.file.SetCellHyperLink(sheetName, filenameCell, abstract.Abstracts.FilePath, "External"); err != nil {
				return nil, fmt.Errorf("failed to set hyperlink: %w", err)
			}
		}
	}

	// Auto-fit columns
	if err := e.autoFitColumns(sheetName, len(headers)); err != nil {
		return nil, fmt.Errorf("failed to auto-fit columns: %w", err)
	}

	// Save to buffer
	buffer, err := e.file.WriteToBuffer()
	if err != nil {
		return nil, fmt.Errorf("failed to write to buffer: %w", err)
	}

	return buffer.Bytes(), nil
}

// styleHeaders applies styling to header row
func (e *ExcelExporter) styleHeaders(sheetName string, colCount int) error {
	// Create header style
	headerStyle, err := e.file.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   12,
			Family: "Arial",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#4472C4"},
			Pattern: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
	})
	if err != nil {
		return err
	}

	// Apply style to header row
	return e.file.SetCellStyle(sheetName, "A1", fmt.Sprintf("%s1", string(rune('A'+colCount-1))), headerStyle)
}

// autoFitColumns adjusts column widths
func (e *ExcelExporter) autoFitColumns(sheetName string, colCount int) error {
	// Set column widths
	columnWidths := map[string]float64{
		"A": 8,  // ID
		"B": 15, // User Name
		"C": 15, // Username
		"D": 25, // Email
		"E": 20, // Organization
		"F": 12, // Country
		"G": 8,  // Gender
		"H": 30, // Title
		"I": 15, // Theme
		"J": 18, // Publication Status
		"K": 15, // Oral Presentation
		"L": 18, // Poster Presentation
		"M": 25, // Filename
		"N": 40, // File Path
		"O": 20, // Submit Time
	}

	for i := 0; i < colCount; i++ {
		col := string(rune('A' + i))
		width := columnWidths[col]
		if width == 0 {
			width = 15 // default width
		}
		if err := e.file.SetColWidth(sheetName, col, col, width); err != nil {
			return err
		}
	}

	return nil
}

// Close closes the Excel file
func (e *ExcelExporter) Close() error {
	return e.file.Close()
}

// GenerateAbstractExportFilename generates a filename for abstract export
func GenerateAbstractExportFilename() string {
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("abstracts_export_%s.xlsx", timestamp)
}
