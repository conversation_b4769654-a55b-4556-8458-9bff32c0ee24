package s3

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/url"
	"next-meeting-backend/internal/pkg/config"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"go.uber.org/zap"
)

var (
	once   sync.Once
	client *minio.Client
	bucket = config.App.S3Config.Bucket
)

type DownloadFileInfo struct {
	minio.ObjectInfo
	*minio.Object
}

func init() {
	client = getClient()
	ctx := context.Background()
	checkBucketExists(ctx, config.App.S3Config.Bucket)
}

func getClient() *minio.Client {
	if client == nil {
		once.Do(func() {
			c, err := minio.New(config.App.S3Config.Endpoint,
				&minio.Options{
					Creds: credentials.NewStaticV4(
						config.App.S3Config.AccessKey,
						config.App.S3Config.SecretKey,
						""),
					Secure: config.App.S3Config.Secure,
					Region: config.App.S3Config.Region,
				})
			if err != nil {
				zap.S().Fatalln("Error creating S3 Client: %v", err)
				os.Exit(1)
			}
			client = c
		})
	}
	return client
}

func checkBucketExists(ctx context.Context, bucketName string) {
	client = getClient()
	e, err := client.BucketExists(ctx, bucketName)
	if err != nil {
		zap.S().Fatalln("Error checking bucket existence: %v", err)
	}
	if !e {
		zap.S().Fatalln("Bucket does not exist")
	}
}

func UploadFileStream(ctx context.Context, uploadedFilename string, contentType consts.ContentType, fileByte []byte, objectSize int64) (*minio.UploadInfo, error) {
	client = getClient()

	updateInfo, err := client.PutObject(ctx, bucket, uploadedFilename, bytes.NewReader(fileByte), objectSize, minio.PutObjectOptions{ContentType: string(contentType)})
	if err != nil {
		zap.S().Errorf("Error uploading file to S3: %v", err)
		return nil, err
	}
	zap.S().Infof("Successfully uploaded %s of size %d\n", uploadedFilename, updateInfo.Size)
	return &updateInfo, err
}

func DownloadFile(ctx context.Context, fileName string) (fileInfo *DownloadFileInfo, err error) {
	client = getClient()

	objInfo, err := client.StatObject(ctx, bucket, fileName, minio.StatObjectOptions{})
	if err != nil {
		zap.S().Errorw("Error getting object info", err)
		return nil, err
	}

	// 获取文件
	object, err := client.GetObject(ctx, bucket, fileName, minio.GetObjectOptions{})
	if err != nil {
		zap.S().Errorw("Error getting object info", err)
		return
	}

	return &DownloadFileInfo{
		ObjectInfo: objInfo,
		Object:     object,
	}, nil
}

func generatePresignedURL(ctx context.Context, fileName string) (string, error) {
	client = getClient()

	reqParams := make(url.Values)
	reqParams.Set("response-content-disposition", "inline; filename=\""+fileName+"\"")

	presignedURL, err := client.PresignedGetObject(ctx, bucket, fileName, time.Minute*30, reqParams)
	if err != nil {
		zap.S().Errorw("Error getting presigned URL", err)
		return "", err
	}
	return presignedURL.String(), nil
}

func GetPresignedURL(ctx context.Context, fileName string, id int) (string, error) {
	if id == 0 {
		err := errors.New("transaction ID is 0, cannot generate presigned URL")
		return "", err
	}
	if fileName == "" {
		return "", errors.New("file name is empty, cannot generate presigned URL")
	}

	v, found := utils.Cache.Get(strconv.Itoa(id))
	if found {
		return v.(string), nil
	}

	// 第二步， 如果不存在则生成新的链接
	presignedURL, err := generatePresignedURL(ctx, fileName)
	if err != nil {
		return "", err
	}
	// 缓存起来
	utils.Cache.SetDefault(strconv.Itoa(id), presignedURL)
	// 返回
	return presignedURL, nil
}

// ProcessAndUploadReceipt 处理并上传收据
func ProcessAndUploadReceipt(ctx context.Context, file multipart.File, fileHeader *multipart.FileHeader, userID int, dir consts.RemoteDir) (string, error) {
	// 读取文件内容
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("读取文件失败: %w", err)
	}

	// 获取文件类型
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))

	compressFile, contentType, err := utils.CompressFile(fileBytes, ext)
	if err != nil {
		return "", err
	}

	// 生成唯一的文件名
	filename := utils.GenerateRemoteFilename(fileHeader.Filename, userID, dir)

	// 上传到MinIO
	updateInfo, err := UploadFileStream(ctx, filename, contentType, compressFile, int64(len(compressFile)))
	if err != nil {
		zap.S().Errorf("upload file to minio failed: %v", err.Error())
		return "", err
	}

	zap.S().Infof("upload file to minio success, filename: %s, user id: %d", filename, userID)
	return updateInfo.Key, nil
}
