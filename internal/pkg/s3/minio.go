package s3

import (
	"archive/zip"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/url"
	"next-meeting-backend/internal/pkg/config"
	"next-meeting-backend/internal/pkg/consts"
	"next-meeting-backend/internal/utils"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"go.uber.org/zap"
)

var (
	once   sync.Once
	client *minio.Client
	bucket = config.App.S3Config.Bucket
)

type DownloadFileInfo struct {
	minio.ObjectInfo
	*minio.Object
}

func init() {
	client = getClient()
	ctx := context.Background()
	checkBucketExists(ctx, config.App.S3Config.Bucket)
}

func getClient() *minio.Client {
	if client == nil {
		once.Do(func() {
			c, err := minio.New(config.App.S3Config.Endpoint,
				&minio.Options{
					Creds: credentials.NewStaticV4(
						config.App.S3Config.AccessKey,
						config.App.S3Config.SecretKey,
						""),
					Secure: config.App.S3Config.Secure,
					Region: config.App.S3Config.Region,
				})
			if err != nil {
				zap.S().Fatalln("Error creating S3 Client: %v", err)
				os.Exit(1)
			}
			client = c
		})
	}
	return client
}

func checkBucketExists(ctx context.Context, bucketName string) {
	client = getClient()
	e, err := client.BucketExists(ctx, bucketName)
	if err != nil {
		zap.S().Fatalln("Error checking bucket existence: %v", err)
	}
	if !e {
		zap.S().Fatalln("Bucket does not exist")
	}
}

func UploadFileStream(ctx context.Context, uploadedFilename string, contentType consts.ContentType, fileByte []byte, objectSize int64) (*minio.UploadInfo, error) {
	client = getClient()

	updateInfo, err := client.PutObject(ctx, bucket, uploadedFilename, bytes.NewReader(fileByte), objectSize, minio.PutObjectOptions{ContentType: string(contentType)})
	if err != nil {
		zap.S().Errorf("Error uploading file to S3: %v", err)
		return nil, err
	}
	zap.S().Infof("Successfully uploaded %s of size %d\n", uploadedFilename, updateInfo.Size)
	return &updateInfo, err
}

func DownloadFile(ctx context.Context, fileName string) (fileInfo *DownloadFileInfo, err error) {
	client = getClient()

	objInfo, err := client.StatObject(ctx, bucket, fileName, minio.StatObjectOptions{})
	if err != nil {
		zap.S().Errorw("Error getting object info", err)
		return nil, err
	}

	// 获取文件
	object, err := client.GetObject(ctx, bucket, fileName, minio.GetObjectOptions{})
	if err != nil {
		zap.S().Errorw("Error getting object info", err)
		return
	}

	return &DownloadFileInfo{
		ObjectInfo: objInfo,
		Object:     object,
	}, nil
}

func generatePresignedURL(ctx context.Context, fileName string) (string, error) {
	client = getClient()

	reqParams := make(url.Values)
	reqParams.Set("response-content-disposition", "inline; filename=\""+fileName+"\"")

	presignedURL, err := client.PresignedGetObject(ctx, bucket, fileName, time.Minute*30, reqParams)
	if err != nil {
		zap.S().Errorw("Error getting presigned URL", err)
		return "", err
	}
	return presignedURL.String(), nil
}

func GetPresignedURL(ctx context.Context, fileName string, id int) (string, error) {
	if id == 0 {
		err := errors.New("transaction ID is 0, cannot generate presigned URL")
		return "", err
	}
	if fileName == "" {
		return "", errors.New("file name is empty, cannot generate presigned URL")
	}

	v, found := utils.Cache.Get(strconv.Itoa(id))
	if found {
		return v.(string), nil
	}

	// 第二步， 如果不存在则生成新的链接
	presignedURL, err := generatePresignedURL(ctx, fileName)
	if err != nil {
		return "", err
	}
	// 缓存起来
	utils.Cache.SetDefault(strconv.Itoa(id), presignedURL)
	// 返回
	return presignedURL, nil
}

// ProcessAndUploadReceipt 处理并上传收据
func ProcessAndUploadReceipt(ctx context.Context, file multipart.File, fileHeader *multipart.FileHeader, userID int, dir consts.RemoteDir) (string, error) {
	// 读取文件内容
	fileBytes, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("读取文件失败: %w", err)
	}

	// 获取文件类型
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))

	compressFile, contentType, err := utils.CompressFile(fileBytes, ext)
	if err != nil {
		return "", err
	}

	// 生成唯一的文件名
	filename := utils.GenerateRemoteFilename(fileHeader.Filename, userID, dir)

	// 上传到MinIO
	updateInfo, err := UploadFileStream(ctx, filename, contentType, compressFile, int64(len(compressFile)))
	if err != nil {
		zap.S().Errorf("upload file to minio failed: %v", err.Error())
		return "", err
	}

	zap.S().Infof("upload file to minio success, filename: %s, user id: %d", filename, userID)
	return updateInfo.Key, nil
}

// FileDownloadInfo represents information about a file to be downloaded
type FileDownloadInfo struct {
	FilePath     string // Path in MinIO
	OriginalName string // Original filename for ZIP entry
}

// DownloadFilesAndCreateZip downloads multiple files from MinIO and creates a ZIP archive with Excel file
func DownloadFilesAndCreateZip(ctx context.Context, files []FileDownloadInfo) ([]byte, error) {
	if len(files) == 0 {
		return nil, errors.New("no files to download")
	}

	// Create a buffer to write ZIP data to
	zipBuffer := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuffer)

	// Download each file and add to ZIP
	for _, fileInfo := range files {
		if fileInfo.FilePath == "" {
			zap.S().Warnf("Skipping file with empty path: %s", fileInfo.OriginalName)
			continue
		}

		// Download file from MinIO
		fileData, err := downloadFileContent(ctx, fileInfo.FilePath)
		if err != nil {
			zap.S().Errorf("Failed to download file %s: %v", fileInfo.FilePath, err)
			// Continue with other files instead of failing completely
			continue
		}

		// Create ZIP entry in abstracts/ subdirectory
		zipEntryName := fmt.Sprintf("abstracts/%s", fileInfo.OriginalName)
		zipEntry, err := zipWriter.Create(zipEntryName)
		if err != nil {
			zap.S().Errorf("Failed to create ZIP entry for %s: %v", fileInfo.OriginalName, err)
			continue
		}

		// Write file content to ZIP entry
		_, err = zipEntry.Write(fileData)
		if err != nil {
			zap.S().Errorf("Failed to write file content to ZIP for %s: %v", fileInfo.OriginalName, err)
			continue
		}

		zap.S().Infof("Added file to ZIP in abstracts/: %s", fileInfo.OriginalName)
	}

	// Close ZIP writer
	err := zipWriter.Close()
	if err != nil {
		return nil, fmt.Errorf("failed to close ZIP writer: %w", err)
	}

	return zipBuffer.Bytes(), nil
}

// CreateZipWithExcelAndFiles creates a ZIP containing Excel file and abstract files
func CreateZipWithExcelAndFiles(ctx context.Context, files []FileDownloadInfo, excelData []byte, excelFilename string) ([]byte, error) {
	zap.S().Infof("Creating ZIP with Excel file (%s) and %d abstract files", excelFilename, len(files))

	// Create a buffer to write ZIP data to
	zipBuffer := new(bytes.Buffer)
	zipWriter := zip.NewWriter(zipBuffer)

	// First, add the Excel file to ZIP root
	if len(excelData) > 0 && excelFilename != "" {
		excelEntry, err := zipWriter.Create(excelFilename)
		if err != nil {
			return nil, fmt.Errorf("failed to create ZIP entry for Excel file: %w", err)
		}

		_, err = excelEntry.Write(excelData)
		if err != nil {
			return nil, fmt.Errorf("failed to write Excel file to ZIP: %w", err)
		}

		zap.S().Infof("Added Excel file to ZIP root: %s", excelFilename)
	}

	// Then download each abstract file and add to ZIP in abstracts/ folder
	for _, fileInfo := range files {
		if fileInfo.FilePath == "" {
			zap.S().Warnf("Skipping file with empty path: %s", fileInfo.OriginalName)
			continue
		}

		// Download file from MinIO
		fileData, err := downloadFileContent(ctx, fileInfo.FilePath)
		if err != nil {
			zap.S().Errorf("Failed to download file %s: %v", fileInfo.FilePath, err)
			continue
		}

		// Create ZIP entry in "abstracts" folder
		zipEntryName := fmt.Sprintf("abstracts/%s", fileInfo.OriginalName)
		zipEntry, err := zipWriter.Create(zipEntryName)
		if err != nil {
			zap.S().Errorf("Failed to create ZIP entry for %s: %v", zipEntryName, err)
			continue
		}

		_, err = zipEntry.Write(fileData)
		if err != nil {
			zap.S().Errorf("Failed to write file content to ZIP for %s: %v", zipEntryName, err)
			continue
		}

		zap.S().Infof("Added abstract file to ZIP: abstracts/%s", fileInfo.OriginalName)
	}

	err := zipWriter.Close()
	if err != nil {
		return nil, fmt.Errorf("failed to close ZIP writer: %w", err)
	}

	zap.S().Infof("ZIP creation completed with Excel and %d abstract files", len(files))
	return zipBuffer.Bytes(), nil
}

// downloadFileContent downloads file content from MinIO
func downloadFileContent(ctx context.Context, fileName string) ([]byte, error) {
	client = getClient()

	// Get file object
	object, err := client.GetObject(ctx, bucket, fileName, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get object: %w", err)
	}
	defer object.Close()

	// Read file content
	content, err := io.ReadAll(object)
	if err != nil {
		return nil, fmt.Errorf("failed to read object content: %w", err)
	}

	return content, nil
}

// DownloadMultipleFiles downloads multiple files concurrently from MinIO
func DownloadMultipleFiles(ctx context.Context, filePaths []string) (map[string][]byte, error) {
	if len(filePaths) == 0 {
		return nil, errors.New("no files to download")
	}

	results := make(map[string][]byte)
	errors := make(map[string]error)

	// Use channels for concurrent downloads
	type downloadResult struct {
		filePath string
		content  []byte
		err      error
	}

	resultChan := make(chan downloadResult, len(filePaths))

	// Start concurrent downloads
	for _, filePath := range filePaths {
		go func(fp string) {
			content, err := downloadFileContent(ctx, fp)
			resultChan <- downloadResult{
				filePath: fp,
				content:  content,
				err:      err,
			}
		}(filePath)
	}

	// Collect results
	for i := 0; i < len(filePaths); i++ {
		result := <-resultChan
		if result.err != nil {
			errors[result.filePath] = result.err
			zap.S().Errorf("Failed to download file %s: %v", result.filePath, result.err)
		} else {
			results[result.filePath] = result.content
		}
	}

	// Log errors but don't fail completely if some files downloaded successfully
	if len(results) == 0 {
		return nil, fmt.Errorf("failed to download any files: %v", errors)
	}

	return results, nil
}
