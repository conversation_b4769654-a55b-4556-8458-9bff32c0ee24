package email

import (
	"bytes"
	"fmt"
	"html/template"
	"next-meeting-backend/internal/pkg/config"
	"next-meeting-backend/internal/pkg/jwt"
	"path/filepath"
	"sync"

	"go.uber.org/zap"

	"gopkg.in/gomail.v2"
)

// Config holds the configuration for email service
type Config struct {
	Host     string
	Port     int
	Username string
	Password string
	From     string
}

// Service represents an email service
type Service struct {
	config       Config
	templatePath string
	templates    map[string]*template.Template
	mu           sync.RWMutex
}

// Global instance of the email service
var instance *Service
var once sync.Once

// NewService creates a new email service
func NewService(config Config, templatePath string) *Service {
	once.Do(func() {
		instance = &Service{
			config:       config,
			templatePath: templatePath,
			templates:    make(map[string]*template.Template),
		}
	})
	return instance
}

// GetEmailService returns the singleton instance of the email service
func GetEmailService() *Service {
	if instance == nil {
		instance = NewService(Config{
			Host:     config.App.EmailConfig.Host,
			Port:     config.App.EmailConfig.Port,
			Username: config.App.EmailConfig.Username,
			Password: config.App.EmailConfig.Password,
			From:     config.App.EmailConfig.From,
		}, config.App.EmailConfig.TemplatePath)
	}
	return instance
}

// LoadTemplate loads a template from the template directory
func (s *Service) LoadTemplate(name string) (*template.Template, error) {
	s.mu.RLock()
	tmpl, ok := s.templates[name]
	s.mu.RUnlock()
	if ok {
		return tmpl, nil
	}

	templatePath := filepath.Join(s.templatePath, name)
	tmpl, err := template.ParseFiles(templatePath)
	if err != nil {
		abs, _ := filepath.Abs(templatePath)
		zap.S().Debug("failed to load template", zap.String("templateName", abs))
		return nil, fmt.Errorf("failed to parse template %s: %w", name, err)
	}

	s.mu.Lock()
	s.templates[name] = tmpl
	s.mu.Unlock()

	return tmpl, nil
}

// SendEmail sends an email with the given template and data
func (s *Service) SendEmail(to []string, subject, templateName string, data interface{}) error {
	tmpl, err := s.LoadTemplate(templateName)
	if err != nil {
		return err
	}

	var body bytes.Buffer
	if err := tmpl.Execute(&body, data); err != nil {
		return fmt.Errorf("failed to execute template: %w", err)
	}

	m := gomail.NewMessage()
	m.SetHeader("From", s.config.From)
	m.SetHeader("To", to...)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body.String())

	d := gomail.NewDialer(s.config.Host, s.config.Port, s.config.Username, s.config.Password)
	if err := d.DialAndSend(m); err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	return nil
}

// SendWelcomeEmail sends a welcome email to a new user
func (s *Service) SendWelcomeEmail(to string, username string) error {
	data := map[string]interface{}{
		"Username": username,
	}
	return s.SendEmail([]string{to}, "Welcome to IFMB Meeting", "email/welcome.html", data)
}

// SendPasswordResetEmail sends a password reset email
func (s *Service) SendPasswordResetEmail(to string, resetLink string) error {
	data := map[string]interface{}{
		"ResetLink": resetLink,
	}
	return s.SendEmail([]string{to}, "Password Reset Request", "email/reset_password.html", data)
}

// SendVerificationEmail sends an email verification link to a new user
func (s *Service) SendVerificationEmail(to string, username string, verificationLink string) error {
	data := map[string]interface{}{
		"Username":         username,
		"VerificationLink": verificationLink,
	}
	return s.SendEmail([]string{to}, "Verify your email - IFMB 2025", "email/verify_email.html", data)
}

func (s *Service) SendPaymentSuccessEmail(to, userName string, transactionId string, amount string, paymentMethod string, paymentDate string, approvalDate string, registrationType string) error {
	data := map[string]interface{}{
		"UserName":         userName,
		"TransactionID":    transactionId,
		"Amount":           amount,
		"PaymentMethod":    paymentMethod,
		"PaymentDate":      paymentDate,
		"ApprovalDate":     approvalDate,
		"RegistrationType": registrationType,
	}
	return s.SendEmail([]string{to}, "Payment Approved - IFMB 2025", "email/payment_success.html", data)
}

func BuildVerifyEmailLink() (token string, verificationLink string) {
	token = jwt.GenerateRandomToken(32)
	verificationLink = fmt.Sprintf("%s/verify-email?token=%s", config.App.WebUrl, token)

	return token, verificationLink
}

func BuildResetPasswordLink() (token string, resetPasswordLink string) {
	token = jwt.GenerateRandomToken(32)
	resetPasswordLink = fmt.Sprintf("%s/reset-password?token=%s", config.App.WebUrl, token)

	return token, resetPasswordLink
}
